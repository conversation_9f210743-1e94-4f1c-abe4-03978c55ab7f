import React, { useEffect, useReducer, useRef, Fragment } from 'react';
import {
  Row,
  Col,
  Tree,
  Select,
  Modal,
  Drawer,
  Descriptions,
  Tag,
  message,
  Radio,
  Checkbox,
  Button, 
} from 'antd';
import { withContext } from 'demasia-pro-layout';
import Ellipsis from '@/components/Ellipsis';
import { Card } from '@/components/PAntd';
import Empty from '@/components/Empty';
import StandardTable from '@/components/StandardTableNew';
import DynamicQuery from '@/components/DynamicQuery1';
import ImportModal from './components/ImportModal';
import ConfigLineModal from './components/ConfigLineModal';
import { connect } from 'dryad';
import { goPage, onEnterPage } from '@/utils/openTab';
import styles from './index.less';
import lodash from 'lodash';
import moment from 'moment';
import TextTooltip from '@/components/TextTooltip';
import { buttonGroup, buttonHasPermission, buttonMap } from './config';
import { objToParam } from '@/utils/utils';
import { exportFile } from '@/utils/exportFile';
import { hyposensitization, desensitizeEmail } from '@/utils/utils';
import { decrypt, encrypt } from '@/utils/EncryptDecryp';
import getColumnWidth from '@/utils/getColumnWidth';
import { commonEllipsis } from '@/utils/commonEllipsis';
const { TreeNode } = Tree;
const { Option } = Select;

// 去除节点   原来去节点findNodeId=306
const filterNode = (currentTree, findNodeId = '306000000') => {
  const _currentTree = Object.assign(currentTree);
  const findNode = (tree = []) => {
    tree.some?.((element, index) => {
      // const res = element.nodeId==findNodeId
      if (element.nodeId == findNodeId) {
        delete tree[index];
        return true;
      }
      if (element.children) {
        findNode(element.children);
      }
    });
  };
  findNode(_currentTree);
  filterNode;
  return _currentTree;
};
const addChild = (treeData, pid, data) => {
  treeData.forEach((item, index) => {
    if (item.nodeId + '' === pid + '') {
      treeData[index].children = data;
    } else if (item.isModel + '' === '0' && treeData[index].children) {
      addChild(treeData[index].children, pid, data);
    }
  });
  return filterNode(treeData);
};

const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px',
};
const mapStateToProps = ({ resourceManagement, loading }) => ({
  resourceManagement,
  loading: loading.effects['resourceManagement/findCiPager'],
});

// 定义初始值
const initialState = {
  resourceCheck: true,
  ExpandedKey: ['0'],
  treeData: [{ nodeName: '全部', isModel: 0, levels: 0, nodeId: 0 }],
  selectedKeys: undefined,
  selectedRows: [],
  pagination: defaultPagination,
  search: '',
  tags: [],
  visible: false,
  ciDetailByCiid: {},
  resourcesType: undefined,
  cabinetNumber: undefined,
  webDomain: undefined,
  resourcesList: [],
  cabinetNumberList: [],
  webDomainList: [],
};

// 定义reducer函数，参数为state值和action动作,想要改变state的特定值的操作都放在reducer中
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}

const ResourceManagement = props => {
  // 初始化userReducer,参数为定义好的reducer函数和initialState（初始状态值）
  const cref = useRef();
  let dataList = [];
  const [state, setState] = useReducer(reducer, initialState);
  const {
    resourceCheck,
    ExpandedKey,
    treeData,
    computerRoomColumns = [
      {
        fieldName: '资源类型',
        fieldCode: 'modelName',
      },
      {
        fieldName: '资源名称',
        fieldCode: 'deviceName',
      },
      {
        fieldName: '资源IP',
        fieldCode: 'ip',
      },
      {
        fieldName: '机柜号',
        fieldCode: 'cabinetId',
      },
      {
        fieldName: 'U位',
        fieldCode: 'cabinetUnit',
      },
      {
        fieldName: '设备品牌',
        fieldCode: 'deviceBrand',
      },
      {
        fieldName: '型号',
        fieldCode: 'model',
      },
      {
        fieldName: '责任处室',
        fieldCode: 'desponSibilityDepartment',
      },
      {
        fieldName: '序列号',
        fieldCode: 'serilNumber',
      },
      {
        fieldName: '用途',
        fieldCode: 'purpost',
      },
      {
        fieldName: '购置时间',
        fieldCode: 'byeTime',
      },
      {
        fieldName: '维保到期时间',
        fieldCode: 'wbdqsj',
      },
      {
        fieldName: '是否过保',
        fieldCode: 'isMaintenanceExpired',
      },
      {
        fieldName: '资源标识',
        fieldCode: 'ciId',
      },
      {
        fieldName: "接入状态",
        fieldCode: "isAccess"
      },
      {
        fieldName: '操作系统',
        fieldCode: 'operatingSystem',
      },
      {
        fieldName: '设备状态',
        fieldCode: 'neWorkingState',
      },
      {
        fieldName: '硬件信息',
        fieldCode: 'capacityValue',
      },
      {
        fieldName: '资产编号',
        fieldCode: 'assetCode',
      },
    ],
    cloudAssetsColumns = [
      {
        fieldName: '资源类型',
        fieldCode: 'modelName',
      },
      {
        fieldName: '实例ID',
        fieldCode: 'instanceUuid',
      },
      {
        fieldName: '资源名称',
        fieldCode: 'deviceName',
      },
      {
        fieldName: '资源IP',
        fieldCode: 'ip',
      },
      {
        fieldName: '所属云区',
        fieldCode: 'deviceBelongField',
      },
      {
        fieldName: '应用名称',
        fieldCode: 'applicationName',
      },
      {
        fieldName: '责任处室',
        fieldCode: 'desponSiblityDepartment',
      },
      {
        fieldName: '责任处室联系人',
        fieldCode: 'desponSiblityDepartmentContacts',
      },
      {
        fieldName: '用途',
        fieldCode: 'purpose',
      },
      {
        fieldName: '开发运维单位',
        fieldCode: 'devopsUnit',
      },
      {
        fieldName: '开发运维人员',
        fieldCode: 'devPeople',
      },
      {
        fieldName: '开发运维联系方式',
        fieldCode: 'phoneNumber',
        isEncrypt: 1,
      },
      {
        fieldName: '备注',
        fieldCode: 'remark',
      },
      {
        fieldName: '资源标识',
        fieldCode: 'ciId',
      },
      {
        fieldName: "接入状态",
        fieldCode: "isAccess"
      },
      {
        fieldName: '资产集名称',
        fieldCode: 'resourceSetName',
      },
    ],
    selectedRows,
    pagination,
    selectedKeys,
    search,
    tags,
    visible,
    ciDetailByCiid,
    topoVisible,
    topoType = 0,
    topoList = [],
    info = {},
    ciId,
    dataupdata = false,
    resourcesType,
    cabinetNumber,
    webDomain,
    resourcesList,
    cabinetNumberList,
    webDomainList,
    configLineVisible = false,
    currentRow = {},
  } = state;

  const {
    dispatch,
    loading,
    resourceManagement: { ciPager = {}, attributeByType, exportCi },
    _activeKey,
    _tabKey,
  } = props;

  const { data = [], total } = ciPager;

  const findChildByParentIdAndRoleId = (pid = 0, reCheck = false) => {
    // 资源分类树最多添加5层分类；超过5层系统提示“最多支持5层分类
    dispatch({
      type: 'resourceManagement/findChildByParentIdAndRoleId',
      payload: {
        pid,
        isNotNull: reCheck,
      },
      callback: childByParentId => {
        const newTreeData = addChild(treeData, pid, childByParentId);
        setState({ treeData: [...newTreeData] });
        if (pid == 0) {
          let ExpandedKeys = ['0'];
          childByParentId.map(item => {
            ExpandedKeys.push(item?.nodeId);
          });
          setState({ ExpandedKey: ExpandedKeys });
          expandFirstLevel();
        }
      },
    });
  };
  const flattenList = data => {
    for (let i = 0; i < data.length; i++) {
      const currentNode = data[i];
      if (currentNode.children && currentNode.children.length > 0) {
        const {
          nodeId,
          nodeName,
          isModel,
          modelId,
          modelType,
          levels,
          nodeDes,
          pid,
          sort,
        } = currentNode;
        dataList.push({
          nodeId,
          nodeName,
          isModel,
          modelId,
          modelType,
          levels,
          nodeDes,
          pid,
          sort,
          sort,
        });
        flattenList(currentNode.children);
      } else {
        dataList.push(currentNode);
      }
    }
  };
  // 展开第一级
  const expandFirstLevel = () => {
    const defaultExpandedKeys = ['0'];
    const arr = [...treeData];
    flattenList(lodash.cloneDeep(arr));
    const index = dataList.findIndex(item => item.isModel == 1 && item.nodeId !== 0);
    if (index == -1 || dataList[index]?.pid == 0) return;
    handleSelectKey('', { node: { props: { dataRef: { ...dataList[index] } } } });
    // if (dataList[index].pid != 0) defaultExpandedKeys.push(dataList[index]?.pid);
    // let index2;
    // for (let i = 0; i <= 5; i++) {
    //   index2 = dataList.findIndex(item => item.nodeId == dataList[index2]?.pid)
    //   if (index2 !== -1 && index2 !== 0) {
    //     defaultExpandedKeys.push(dataList[index2].nodeId)
    //   }else{
    //     setState({defaultExpandedKeys})
    //     return false;
    //   }
    // }
  };

  useEffect(() => {
    findChildByParentIdAndRoleId(0, true);
    // expandFirstLevel();
  }, [0]);

  useEffect(() => {
    // handleSearch({ tags, pagination });
    const unlistenHistory = onEnterPage(props, () => {
      if (selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产') {
        computerRoomSearch({ nodeId: selectedKeys.nodeId, nodeName: selectedKeys.nodeName, tags });
      } else {
        handleSearch({ tags, pagination });
      }
    });
    return () => {
      if (unlistenHistory) unlistenHistory();
    };
    // if (selectedKeys && _activeKey === _tabKey) {
    //   handleSearch({ tags, pagination });
    // }
    // }, [_activeKey]);
  }, [selectedKeys]);

  const renderTreeNodes = data => {
    return data.map(item => {
      const title = (
        <Ellipsis
          tooltip
          length={15}
          style={{
            color: item.nodeName == '厅中心机房' || item.nodeName == '政务云资产' ? '#1890ff' : '',
          }}
        >
          {item.isModel * 1.0 === 0 ? item.nodeName : item.modelName}
        </Ellipsis>
      );

      if (item.children) {
        return (
          <TreeNode
            title={title}
            key={item.nodeId}
            dataRef={item}
            isLeaf={!!(item.isModel * 1.0)}
            blockNode={true}
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          blockNode={true}
          {...item}
          title={title}
          key={item.nodeId}
          dataRef={item}
          isLeaf={!!(item.isModel * 1.0)}
        />
      );
    });
  };

  const handleSearch = (params = {}) => {
    // if (!selectedKeys && params.length === 0) return;
    let { pagination = {}, tags = [], search = '', ...rest } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };

    setState({ pagination: newPagination });
    if (tags.length > 0) {
      search = tags?.length > 0 ? JSON.stringify(tags) : '';
    }
    setState({ selectedRows: [], search });
    const param = {
      ...selectedKeys,
      sidx: '',
      sord: '',
      page: newPagination.current,
      limit: newPagination.pageSize,
      type: selectedKeys && selectedKeys.modelType ? selectedKeys.modelType : params.modelType,
      resourceCheck: resourceCheck,
      resourcesType: resourcesType,
      cabinetNumber: cabinetNumber,
      webDomain: webDomain,
      search,
      ...rest,
    };
    dispatch({
      type: 'resourceManagement/findCiPager',
      payload: param,
    });
  };

  // 厅中心机房 |  政务云资产
  const computerRoomSearch = (params = {}) => {
    // if (!selectedKeys && params.length === 0) return;
    let { pagination = {}, tags = [], search = '', nodeName, ...rest } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };
    setState({ pagination: newPagination });
    if (tags.length > 0) {
      search = tags?.length > 0 ? JSON.stringify(tags) : '';
    }
    setState({ selectedRows: [], search });
    const param = {
      sidx: '',
      sord: '',
      current: newPagination.current,
      size: newPagination.pageSize,
      modelType: resourcesType,
      cabinetUnit: cabinetNumber,
      belongDomain: webDomain,
      search,
      ...rest,
    };
    if (nodeName == '厅中心机房') {
      dispatch({
        type: 'resourceManagement/getWaterResourcesAll',
        payload: param,
      });
    } else {
      dispatch({
        type: 'resourceManagement/getGovernResourcesPage',
        payload: param,
      });
    }
  };

  const findAttributeByType = modelId => {
    dispatch({
      type: 'resourceManagement/findAttributeByType',
      payload: { modelId },
    });
  };
  const getSelectOption = (nodeId, nodeName) => {
    dispatch({
      type: 'resourceManagement/getWaterResourcesType',
      payload: { nodeId },
      callback: res => {
        setState({
          resourcesList: res.resData,
        });
      },
    });
    if (nodeName == '厅中心机房') {
      dispatch({
        type: 'resourceManagement/getWaterCabinetNumber',
        payload: { nodeId },
        callback: res => {
          setState({
            cabinetNumberList: res.resData,
          });
        },
      });
    } else {
      dispatch({
        type: 'resourceManagement/getDeviceBelongField',
        payload: { nodeId },
        callback: res => {
          setState({
            webDomainList: res.resData,
          });
        },
      });
    }
  };

  // kkkk
  const handleSelectKey = (selectedKeys, e) => {
    const dataRef = e.node.props.dataRef;
    // console.log(selectedKeys, dataRef);
    if (
      dataRef.isModel * 1.0 ||
      dataRef.nodeName == '厅中心机房' ||
      dataRef.nodeName == '政务云资产'
    ) {
      setState({ selectedKeys: dataRef });
      if (dataRef.nodeName == '厅中心机房' || dataRef.nodeName == '政务云资产') {
        getSelectOption(dataRef.nodeId, dataRef.nodeName);
        computerRoomSearch({ nodeId: dataRef.nodeId, nodeName: dataRef.nodeName });
      } else {
        findAttributeByType(dataRef.modelId);
        handleSearch({ ...dataRef, type: dataRef.modelType });
      }
    }
  };
  const handleExpand = (expandedKeys, e) => {
    // console.log(expandedKeys, e);
    setState({ ExpandedKey: expandedKeys });
  };
  const handleSelectRows = rows => {
    setState({
      selectedRows: rows,
    });
  };

  const handlePaginationTable = (pagination, filters, sorter) => {
    if (selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产') {
      computerRoomSearch({
        pagination,
        sidx: sorter?.field,
        search,
        sord: sorter?.order === 'descend' ? 'DESC' : 'ASC',
        nodeId: selectedKeys.nodeId,
        nodeName: selectedKeys.nodeName,
      });
    } else {
      handleSearch({
        pagination,
        sidx: sorter?.field,
        search,
        sord: sorter?.order === 'descend' ? 'DESC' : 'ASC',
      });
    }
  };

  const handleDelete = selctedList => {
    Modal.confirm({
      title: '删除确认',
      content: `是否确定删除该资源信息?`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if(selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产'){
          let ciids = '';
          selctedList.forEach(item => {
            ciids += `${item.ciId},`;
          });
          ciids = ciids.substring(0, ciids.length - 1);
          dispatch({
            type: 'resourceManagement/deleteCi',
            payload: {
              modelId: selctedList[0]?.modelId,
              type: selctedList[0]?.modelType,
              ciids: ciids,
            },
            callback: () => {
              setState({ selectedRows: [] });
              computerRoomSearch({nodeId: selectedKeys.nodeId, nodeName: selectedKeys.nodeName,tags,});
            },
          });
        }else{
          let ciids = '';
          selctedList.forEach(item => {
            ciids += `${item.CI_ID},`;
          });
          ciids = ciids.substring(0, ciids.length - 1);
          dispatch({
            type: 'resourceManagement/deleteCi',
            payload: {
              modelId: selectedKeys.modelId,
              type: selectedKeys.modelType,
              ciids: ciids,
            },
            callback: () => {
              setState({ selectedRows: [] });
              handleSearch({ search });
            },
          });
        }
      },
    });
  };

  // 配线
  const handleLine = record => {
    setState({ currentRow: record, configLineVisible: true });
  };

  // 数据采集
  const changethisTableData = row => {
    // console.log(row);
    Modal.confirm({
      title: '数据采集确认',
      content: `是否确定进行数据采集?`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        setTimeout(() => {
          message.success('采集数据成功');
          data.forEach(el => {
            if (el.CI_ID === row.CI_ID) {
              el.CI_UPDATE_TIME = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
            }
          });
          setState({ dataupdata: !dataupdata });
        }, 60 * 1000);
      },
    });
  };

  const handleExportCi = () => {
    if (!selectedKeys) return;
    const {search,resourcesType,cabinetNumber,webDomain} = state
    // console.log(selectedKeys);

    console.log(search,88888)
    if(selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产'){
      let  url =selectedKeys?.nodeName == '厅中心机房' ? "/api/cmdb/ci/waterResourcesExport " : "/api/cmdb/ci/governResourcesExport"
      // let  search = search || ''
      let params={
        nodeId: selectedKeys.nodeId,
        nodeName: selectedKeys.nodeName,
        modelType: resourcesType,
        cabinetUnit: cabinetNumber,
        belongDomain: webDomain,
      }
      // window.location.href = url + objToParam(params);
      exportFile({
        urlAPi: url,
        method: 'POST',
        data: {
          ...params,
        },
        requestType: 'form',
        decodeURI: true,
      }).finally(res => {
       
      });


    }else{
      const param = {
        modelId: selectedKeys.modelId,
        type: selectedKeys.modelType,
        sidx: '',
        sord: '',
        type: selectedKeys.modelType,
        nodeId: selectedKeys.nodeId,
        search: search || '',
        modelName: selectedKeys.modelName,
      };
      dispatch({
        type: 'resourceManagement/exportCi',
        payload: param,
      });
    }
    
  };
  const handleImportCi = () => {
    cref.current.handleImprot();
  };
  const handleClickDetail = (record = {}, type, treeSelect = {}) => {
    // let rs = '';
    if (type === 'add') {
      // rs = '新增';
      if (selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产') {
        goPage(`/resourceCenter/resourceAddComputerAssets`, {
          ...selectedKeys,
          type,
          ...record,
          children: [],
        });
      } else {
        goPage(`/resourceCenter/resourceDetail`, {
          ...selectedKeys,
          type,
          ...record,
          children: [],
        });
      }
    } else if (type === 'edit') {
      // rs = '编辑';
      if(selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产'){
        goPage(`/resourceCenter/resourceDetail`, {
          ...selectedKeys,
          ...record,
          modelId: record?.modelId,
          nodeId: record?.nodeId,
          selectedNodeId: selectedKeys?.nodeId,
          modelType: record?.modelType,
          modelName: record?.modelName,
          nodeName: record?.nodeName,
          pid: record?.pid,
          CI_ID: record?.ciId,
          type,
          children: [],
        });
      }else{
        goPage(`/resourceCenter/resourceDetail`, {
          ...selectedKeys,
          type,
          ...record,
          children: [],
        });
      }
    } else if (type === 'view') {
      dispatch({
        type: 'resourceManagement/findCiDetailByCiid',
        payload: {
          modelId: treeSelect?.modelId,
          nodeId: treeSelect?.nodeId,
          type: treeSelect?.modelType,
          ciid: record.CI_ID,
        },
        callback: ciDetailByCiid => {
          setState({ visible: true, ciDetailByCiid });
        },
      });
    }
  };

  const handleClose = () => {
    setState({ visible: false });
  };
  const handleConnect = record => {
    if (!record.MANAGE_IP) {
      message.error('请填写管理口IP地址');
      return false;
    }
    const url = `http://${record.MANAGE_IP}/`;
    window.open(url, '_blank');
  };
  const judgeType = record => {
    dispatch({
      type: 'resourceManagement/judgetoptype',
      payload: { ciId: record.CI_ID },
      callback: res => {
        if (res.status == '200') {
          if (!res.data.outputData || res.data.outputData.length == 0) {
            message.error('暂无拓扑');
          } else if (res.data.outputData.length > 1) {
            setState({
              topoVisible: true,
              ciId: record.CI_ID,
              topoList: res.data.outputData,
              info: record,
            });
          } else {
            goPage(`/resourceTopology/${record.CI_ID}`, {
              type: res.data.outputData[0],
              nodeId: selectedKeys.nodeId,
              modelId: selectedKeys.modelId,
              moduleType: selectedKeys?.modelType,
              modelName: record.DEVICE_NAME ? record.DEVICE_NAME : selectedKeys.modelName,
            });
          }
        } else {
          message.destroy();
          message.error(res.msg);
        }
      },
    });
  };

  // 计算文本宽度的函数
  const calculateTextWidth = (text, fontSize = 14) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    context.font = `${fontSize}px Arial`;
    return context.measureText(text).width;
  };



  const columns = (selectedKeys?.nodeName == '厅中心机房'
    ? computerRoomColumns
    : selectedKeys?.nodeName == '政务云资产'
    ? cloudAssetsColumns
    : attributeByType
  )
    .map(v => ({
      title: v.fieldName,
      dataIndex: v.fieldCode,
      key: v.fieldCode,
      sorter: true,
      width: v.width ??getColumnWidth(v.fieldName),
      // width: v.width ??160,
      align: 'center',
      render: text => {
        const newText = v?.isEncrypt ? hyposensitization(decrypt(text)) : text;
        return commonEllipsis(newText);
        // return <TextTooltip width={v.width ?? getColumnWidth(v.fieldName)}>{newText}</TextTooltip>;
      },
    }))
    .concat([
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: 280,
        render: (text, record) => {
          const buttonPerString = record?.OPERATOR_BUTTON || record?.operatorButton;
          // let modelName = selectedKeys?.modelName;
          // if (!buttonGroup[modelName]) modelName = '业务系统';
          const getDisabled = type => {
            if (!buttonPerString) return true;
            return String(buttonPerString).indexOf(type) === -1;
          };
          return (
            <div className={styles.operationCol}>
              {/* {selectedKeys.modelType == 'BCS' || selectedKeys.modelType == 'PCS' ? <a
                style={{ marginRight: '6px' }}
                onClick={() => {
                  handleConnect(record)
                }}
              >直连</a> : ""} */}
              {/* <a
                style={{ marginRight: '5px' }}
                onClick={() => {
                  goPage('/resourceCenter/resourceRelationship/' + record.CI_ID, {
                    moduleName: selectedKeys.modelName
                  })
                }}
              >
                关系
              </a> */}
              {!getDisabled('拓扑') && (
                <Button
                  disabled={getDisabled('拓扑')}
                  type="link"
                  onClick={() => judgeType(record)}
                >
                  拓扑
                </Button>
              )}
              {!getDisabled('监控') && (
                <Button
                  type="link"
                  disabled={getDisabled('监控')}
                  onClick={() => {
                    if (
                      selectedKeys?.nodeName == '厅中心机房' ||
                      selectedKeys?.nodeName == '政务云资产'
                    ) {
                      goPage(`/deviceMonitorDetail/${record.ciId}`, {
                        pname: record.cabinetId,
                        modelName: record.modelName,
                        ciId: record.ciId,
                        moduleType: record?.modelType,
                      });
                    } else {
                      goPage(`/deviceMonitorDetail/${record.CI_ID}`, {
                        pname: selectedKeys.pname,
                        modelName: selectedKeys.modelName,
                        ciId: record.CI_ID,
                        moduleType: selectedKeys?.modelType,
                      });
                    }
                  }}
                >
                  监控
                </Button>
              )}
              {!getDisabled('详情') && (
                <Button
                  disabled={getDisabled('详情')}
                  type="link"
                  onClick={() => {
                    if (
                      selectedKeys?.nodeName == '厅中心机房' ||
                      selectedKeys?.nodeName == '政务云资产'
                    ) {
                      dispatch({
                        type: 'resourceManagement/findCiDetailByCiid',
                        payload: {
                          modelId: record.modelId,
                          nodeId: record.nodeId,
                          type: record.modelType,
                          ciid: record.ciId,
                        },
                        callback: ciDetailByCiid => {
                          setState({ visible: true, ciDetailByCiid });
                        },
                      });
                    } else {
                      handleClickDetail(record, 'view', selectedKeys);
                    }
                  }}
                >
                  详情
                </Button>
              )}
              {!getDisabled('编辑') && (
                <Button
                  disabled={getDisabled('编辑')}
                  type="link"
                  onClick={() => {
                    handleClickDetail(record, 'edit');
                  }}
                >
                  编辑
                </Button>
              )}
              {!getDisabled('删除') && (
                <Button
                  disabled={getDisabled('删除')}
                  type="link"
                  onClick={() => {
                    handleDelete([record]);
                  }}
                >
                  删除
                </Button>
              )}
              {/* <Button
                type="link"
                onClick={() => {
                  changethisTableData(record);
                }}
              >
                数据采集
              </Button> */}
              {!getDisabled('配线') && (
                <Button
                  disabled={getDisabled('配线')}
                  type="link"
                  onClick={() => {
                    handleLine(record);
                  }}
                >
                  配线
                </Button>
              )}
            </div>
          );
        },
      },
    ]);

  const handleModuleChange = (val, type) => {
    setState({ [type]: val });
  };

  const transformValue = (record, v) => {
    // 当选中项时业务系统时，需要特殊处理字段
    if(selectedKeys?.modelId == '506'){
      const specialFields = ['SYSTEM_MASTER', 'DESPONSIBILITY_DEPARTMENT'];
      if(specialFields.includes(v.fieldCode)){
        return record?.[v.fieldCode + '_NAME'];
      }
      return record?.[v.fieldCode];
    }
    return record?.[v.fieldCode];
  };

  return (
    <div className={styles.resourceManagement}>
      <Row gutter={8} type="flex">
        <Col span={4}>
          <Card title="资源导航配置" style={{ minHeight: '1020px' }}>
            <div>
              <Checkbox
                checked={resourceCheck}
                onChange={e => {
                  setState({ resourceCheck: e.target.checked });
                  findChildByParentIdAndRoleId(0, e.target.checked);
                }}
                style={{ marginRight: 6 }}
              />
              隐藏无资产分支
            </div>
            <Tree
              // showLine={true}
              onSelect={handleSelectKey}
              onExpand={handleExpand}
              expandedKeys={ExpandedKey}
              // defaultExpandAll={true}
              // defaultExpandParent={true}
              // defaultExpandAll={true}
            >
              {renderTreeNodes(treeData)}
            </Tree>
          </Card>
        </Col>
        <Col span={20}>
          <Card title="资源查询" bodyStyle={{ marginBottom: '10px' }}>
            <DynamicQuery
              selectedKeys={selectedKeys}
              value={tags}
              onChange={tags => {
                setState({ tags });
              }}
              onReset={() => {
                setState({
                  resourcesType: undefined,
                  cabinetNumber: undefined,
                  webDomain: undefined,
                });
                if (
                  selectedKeys?.nodeName == '厅中心机房' ||
                  selectedKeys?.nodeName == '政务云资产'
                ) {
                  computerRoomSearch({
                    nodeId: selectedKeys.nodeId,
                    nodeName: selectedKeys.nodeName,
                    tags,
                    modelType: undefined,
                    cabinetUnit: undefined,
                    belongDomain: undefined,
                  });
                } else {
                  handleSearch({ tags });
                }
              }}
              onSearch={tags => {
                if (
                  selectedKeys?.nodeName == '厅中心机房' ||
                  selectedKeys?.nodeName == '政务云资产'
                ) {
                  computerRoomSearch({
                    nodeId: selectedKeys.nodeId,
                    nodeName: selectedKeys.nodeName,
                    tags,
                  });
                } else {
                  handleSearch({ tags });
                }
              }}
            >
              {selectedKeys?.nodeName == '厅中心机房' || selectedKeys?.nodeName == '政务云资产' ? (
                <Fragment>
                  <span style={{ whiteSpace: 'nowrap' }}>资源类型：</span>
                  <Select
                    maxTagTextLength={6}
                    allowClear={true}
                    style={{ width: 160, marginRight: '15px' }}
                    placeholder="请选择"
                    onChange={value => {
                      handleModuleChange(value, 'resourcesType');
                    }}
                    value={resourcesType}
                  >
                    {resourcesList?.map(item => (
                      <Option maxTagTextLength={6} key={item.modelType} value={item.modelName}>
                        {item.modelName}
                      </Option>
                    ))}
                  </Select>
                </Fragment>
              ) : (
                ''
              )}
              {selectedKeys?.nodeName == '厅中心机房' ? (
                <Fragment>
                  <span style={{ whiteSpace: 'nowrap' }}>机柜号：</span>
                  <Select
                    maxTagTextLength={6}
                    allowClear={true}
                    style={{ width: 160, marginRight: '15px' }}
                    placeholder="请选择"
                    onChange={value => {
                      handleModuleChange(value, 'cabinetNumber');
                    }}
                    value={cabinetNumber}
                  >
                    {cabinetNumberList?.map(item => (
                      <Option maxTagTextLength={6} key={item.nodeId} value={item.nodeName}>
                        {item.nodeName}
                      </Option>
                    ))}
                  </Select>
                </Fragment>
              ) : (
                ''
              )}
              {selectedKeys?.nodeName == '政务云资产' ? (
                <Fragment>
                  <span style={{ whiteSpace: 'nowrap' }}>所属云区：</span>
                  <Select
                    maxTagTextLength={6}
                    allowClear={true}
                    style={{ width: 160, marginRight: '15px' }}
                    placeholder="请选择"
                    onChange={value => {
                      handleModuleChange(value, 'webDomain');
                    }}
                    value={webDomain}
                  >
                    {webDomainList?.map(item => (
                      <Option maxTagTextLength={6} key={item.modelType} value={item.nodeName}>
                        {item.nodeName}
                      </Option>
                    ))}
                  </Select>
                </Fragment>
              ) : (
                ''
              )}
              {selectedKeys && <Tag color="#2db7f5">{selectedKeys.modelName}</Tag>}
            </DynamicQuery>
          </Card>
          <Card title="资源列表" style={{ minHeight: '900px' }}>
            {!selectedKeys ? (
              <Empty description="请选择资源导航配置" height="425px" />
            ) : (
              <div>
                <div style={{ marginBottom: '20px', display: 'none' }}>
                  <ImportModal
                    selectedKeys={selectedKeys}
                    handleSearch={handleSearch}
                    cref={cref}
                    resourcesList={resourcesList}
                  />
                </div>
                <StandardTable
                  buttonList={[
                    {
                      text: '添加',
                      type: 'primary',
                      isGhost: false,
                      features: () => handleClickDetail(selectedKeys, 'add'),
                    },
                    {
                      text: '删除',
                      type: 'default',
                      isGhost: false,
                      disabled: selectedRows.length === 0,
                      features: () => handleDelete(selectedRows),
                    },
                    {
                      text: '导出',
                      type: 'default',
                      isGhost: false,
                      disabled: !data || data.length === 0,
                      features: () => handleExportCi(),
                    },
                    {
                      text: '导入',
                      type: 'default',
                      isGhost: false,
                      features: () => handleImportCi(),
                    },
                  ]}
                  rowKey="CI_ID"
                  tools={true}
                  size="small"
                  tableAlert={false}
                  loading={!!loading}
                  dataupdata={dataupdata}
                  data={{
                    list: data,
                    pagination: {
                      // showSizeChanger: false,
                      pageSizeOptions: ['10', '20', '30', '40'],
                      ...pagination,
                      total,
                    },
                  }}
                  columns={columns}
                  selectedRows={selectedRows}
                  onSelectRow={handleSelectRows}
                  onChange={handlePaginationTable}
                  scroll={{ x: '100%' }}
                  useEllipsis={true}
                />
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 详情信息 */}
      <Drawer
        title="详情信息"
        placement="right"
        closable={false}
        width={700}
        onClose={handleClose}
        visible={visible}
        destroyOnClose={true}
      >
        <Descriptions column={2}>
          {attributeByType.map(v => {
            return (
              <Descriptions.Item label={v.fieldName}>

                {v.isEncrypt
                  ? hyposensitization(decrypt(ciDetailByCiid[v.fieldCode]))
                  : transformValue(ciDetailByCiid, v)}
              </Descriptions.Item>
            );
          })}
        </Descriptions>
      </Drawer>

      {/* 拓扑类型选择 */}
      <Modal
        title="拓扑类型选择"
        visible={topoVisible}
        width={360}
        onCancel={() => {
          setState({ topoVisible: false, topoType: 0 });
        }}
        onOk={() => {
          goPage(`/resourceCenter/resourceTopology/${ciId}`, {
            type: topoType,
            nodeId: selectedKeys.nodeId,
            modelId: selectedKeys.modelId,
            moduleType: selectedKeys.modelType,
            modelName: info.DEVICE_NAME ? info.DEVICE_NAME : info.modelName,
          });
          setState({ topoVisible: false, topoType: 0 });
        }}
      >
        <Radio.Group
          onChange={e => {
            setState({ topoType: e.target.value });
          }}
          value={topoType}
        >
          {topoList?.map(item => (
            <Radio style={radioStyle} value={item}>
              {item == 0 ? '业务拓扑' : item == 1 ? '虚拟拓扑' : '一般拓扑'}
            </Radio>
          ))}
        </Radio.Group>
      </Modal>
      {/* 配线信息 */}
      {configLineVisible && (
        <ConfigLineModal
          visible={configLineVisible}
          onCancel={() => {
            setState({ configLineVisible: false, currentRow: {} });
          }}
          currentRow={currentRow}
          modelType={selectedKeys?.modelType}
        />
      )}
    </div>
  );
};

const config = [
  ['activeKey', 'tabKey'],
  ['_activeKey', '_tabKey'],
];
const YourComponent = withContext(...config)(props => {
  return <ResourceManagement {...props} />;
});

export default connect(mapStateToProps, null)(YourComponent);
