import React, { useEffect, useState, useRef, Fragment } from 'react';
import StandardTableNew from '@/components/StandardTableNew';
import { commonEllipsis } from '@/utils/commonEllipsis';
import { Modal, Form, Button, Row, Col, Select, message, Input } from 'antd';
import request from 'ponshine-request';
import AddModal from './AddModal';
import ImportDeviceModal from './ImportDeviceModal';
import { exportFile } from '@/utils/exportFile';

// 将接口返回的嵌套对象数据（机柜->设备->端口项数组）转换为扁平化表格数据，并添加合并标记
const transformApiData = dataObj => {
  if (!dataObj || typeof dataObj !== 'object') return [];
  const flattenedData = [];
  let rowIndex = 0;
  Object.keys(dataObj).forEach(cabinetKey => {
    const deviceObj = dataObj[cabinetKey];
    // 计算当前机柜下所有端口项的总数
    const cabinetRowSpan = Object.values(deviceObj).reduce((sum, arr) => sum + arr.length, 0);
    let cabinetFirstRow = true;
    Object.keys(deviceObj).forEach(deviceKey => {
      const portArr = deviceObj[deviceKey];
      const deviceRowSpan = portArr.length;
      let deviceFirstRow = true;
      portArr.forEach(item => {
        flattenedData.push({
          ...item,
          key: `row-${rowIndex++}`,
          // 合并标记
          cabinet: cabinetKey,
          deviceName: deviceKey,
          cabinetRowSpan: cabinetFirstRow ? cabinetRowSpan : 0,
          deviceRowSpan: deviceFirstRow ? deviceRowSpan : 0,
        });
        cabinetFirstRow = false;
        deviceFirstRow = false;
      });
    });
  });
  return flattenedData;
};

const ConfigLineModal = ({ visible, onCancel, currentRow, form, modelType }) => {
  const [tableData, setTableData] = useState({ list: [], pagination: {} });
  const [cabinetList, setCabinetList] = useState({});
  const [deviceList, setDeviceList] = useState([]);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [editCurrentRow, setEditCurrentRow] = useState({});
  const searchParams = useRef({});

  const columns = [
    {
      title: '机柜',
      dataIndex: 'cabinet',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: (text, record) => {
        const obj = {
          children: commonEllipsis(text),
          props: {
            rowSpan: record.cabinetRowSpan,
            style: {
              borderRight: '1px solid #e8e8e8',
              borderBottom: record.cabinetRowSpan > 0 ? '1px solid #e8e8e8' : 'none',
            },
          },
        };
        return obj;
      },
    },
    
    {
      title: '资源名称',
      dataIndex: 'deviceName',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: (text, record) => {
        const obj = {
          children: commonEllipsis(text),
          props: {
            rowSpan: record.deviceRowSpan,
            style: {
              borderRight: '1px solid #e8e8e8',
              borderBottom: record.deviceRowSpan > 0 ? '1px solid #e8e8e8' : 'none',
            },
          },
        };
        return obj;
      },
    },
    
    {
      title: '本端IP',
      dataIndex: 'portIp',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderRight: '1px solid #e8e8e8', borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: '端口名称',
      dataIndex: 'portName',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderRight: '1px solid #e8e8e8', borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: '本端配线架',
      dataIndex: 'odfIdFrom',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderRight: '1px solid #e8e8e8', borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: '对端配线架编号',
      dataIndex: 'odfIdTo',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderRight: '1px solid #e8e8e8', borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: '对端资源名称',
      dataIndex: 'deviceNameTo',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: '对端端口名称',
      dataIndex: 'portIdTo',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: 'KVM连接信息',
      dataIndex: 'kvmInfo',
      align: 'center',
      width: 180,
      ellipsis: true,
      render: text => ({
        children: commonEllipsis(text),
        props: {
          style: { borderBottom: '1px solid #e8e8e8' },
        },
      }),
    },
    {
      title: '操作',
      dataIndex: 'opt',
      align: 'center',
      width: 180,
      ellipsis: true,
      fixed: 'right',
      render: (text, record) => {
        return (
          <Fragment>
            <Button type="link" onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Button type="link" onClick={() => handleDelete(record)}>
              删除
            </Button>
          </Fragment>
        );
      },
    },
  ];

  // 处理新增
  const handleAdd = () => {
    setAddModalVisible(true);
  };

  const handleEdit = record => {
    setEditCurrentRow(record);
    setAddModalVisible(true);
  };

  // 处理新增确认
  const handleAddOk = async values => {
    const url = `/api/cmdb/ci/${values?.id ? 'editMdfOtherInfo' : 'addMdfOtherInfo'}`;
    const response = await request(url, {
      method: 'POST',
      data: { ...values, nodeId: currentRow.NODE_ID, type: modelType },
      requestType: 'form',
    });
    if (response.code === 200) {
      message.success(response.msg);
      // 关闭新增弹窗
      setAddModalVisible(false);
      getTableData(searchParams.current);
      getCabinetListData(values.cabinetId);

    } else {
      message.error(response.msg);
    }
  };

  const getCabinetListData = async cabinetId => {
    const response = await request('/api/cmdb/ci/getMdfQueryCondition', {
      method: 'POST',
      data: {
        type: modelType,
      },
    });
    if (response.code === 200) {
      setCabinetList(response.resData);
      if (cabinetId) {
        setDeviceList(response?.resData?.[cabinetId]);
      }
    } else {
      message.error(response.message);
      setCabinetList({});
    }
  };

  const handleCabinetChange = value => {
    form.setFieldsValue({
      deviceName: undefined,
    });
    if (value) {
      setDeviceList(cabinetList[value] || []);
    } else {
      setDeviceList([]);
    }
  };

  const getTableData = async ({ current = 1, size = 10, ...params } = {}) => {
    const response = await request('/api/cmdb/ci/getMdfDetailPage', {
      method: 'POST',
      data: {
        ...params,
        current,
        size,
      },
      requestType: 'form',
    });
    if (response.code === 200) {
      searchParams.current = {
        ...params,
        current,
        size,
      };
      // 处理接口返回的嵌套对象数据
      const list = transformApiData(response?.resData?.data);
      setTableData({
        list,
        pagination: {
          total: response?.resData?.total || 0,
          pageSize: size,
          current,
        },
      });
    } else {
      message.error(response.msg);
    }
  };

  const handleReset = () => {
    form.setFieldsValue({
      cabinetId: undefined,
      deviceName: undefined,
    });
    getTableData();
  };

  const handleSearch = () => {
    const params = form.getFieldsValue();
    getTableData(params);
  };

  const handleChange = pagination => {
    getTableData({
      ...searchParams.current,
      current: pagination.current,
      size: pagination.pageSize,
    });
  };

  const handleDelete = record => {
    Modal.confirm({
      title: '确定删除吗？',
      onOk: async () => {
        const response = await request('/api/cmdb/ci/deleteMdfOtherInfo', {
          method: 'POST',
          data: { id: record.id },
          requestType: 'form',
        });
        if (response.code === 200) {
          message.success(response.msg);
          getTableData({ ...searchParams.current, current: 1, size: 10 });
        } else {
          message.error(response.msg);
        }
      },
    });
  };

  const handleImport = () => {
    setImportModalVisible(true);
  };

  const handleExport = () => {
    exportFile({
      urlAPi: '/api/cmdb/ci/exportMdfOtherDetail',
      method: 'POST',
      data: searchParams.current,
      requestType: 'form',
      decodeURI: true,
    }).finally(res => {});
  };

  const handleImportOk = async file => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('nodeId', currentRow.NODE_ID);
    formData.append('type', modelType);
    const response = await request('/api/cmdb/ci/importMdfOtherDetail', {
      method: 'POST',
      data: formData,
      requestType: 'form',
    });
    if (response.code === 200) {
      message.success(response.msg);
      handleReset();
      setImportModalVisible(false);
    } else {
      message.error(response.msg);
    }
  };

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        cabinetId: currentRow.CABINET_ID2,
        deviceName: currentRow.DEVICE_NAME,
      });
      getCabinetListData(currentRow.CABINET_ID2);

      getTableData({
        cabinetId: currentRow.CABINET_ID2,
        deviceName: currentRow.DEVICE_NAME,
      });
    }
  }, [visible]);
  return (
    <Modal
      title="配线详细信息"
      footer={false}
      visible={visible}
      onCancel={onCancel}
      width={'85%'}
      maskClosable={false}
      destroyOnClose
    >
      <Form wrapperCol={{ span: 16 }} labelCol={{ span: 8 }}>
        <Row>
          <Col span={8}>
            <Form.Item label="机柜">
              {form.getFieldDecorator('cabinetId', {
                initialValue: currentRow.CABINET_ID2,
              })(
                <Select placeholder="请选择机柜" allowClear onChange={handleCabinetChange}>
                  {Object.keys(cabinetList).map(ele => (
                    <Select.Option value={ele}>{ele}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="资源名称">
              {form.getFieldDecorator('deviceName', {
                initialValue: currentRow.DEVICE_NAME,
              })(
                <Select placeholder="请选择资源名称" allowClear>
                  {deviceList?.map(ele => (
                    <Select.Option value={ele.deviceName}>{ele.deviceName}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="本端IP">
              {form.getFieldDecorator('portIp', {
                // initialValue: currentRow.portIp,
              })(
                <Input placeholder="请输入本端IP" allowClear />
              )}
            </Form.Item>
          </Col>
          
          <Col span={24}>
            <Form.Item wrapperCol={{ span: 24 }} style={{ textAlign: 'right' }}>
              <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset} style={{ marginRight: 16 }}>
                重置
              </Button>
            </Form.Item>
          </Col>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button type="primary" style={{ marginRight: 16 }} onClick={handleAdd}>
              添加
            </Button>
            {/* <Button onClick={handleDelete} type="danger" style={{ marginRight: 16 }}>
              删除
            </Button> */}
            <Button style={{ marginRight: 16 }} onClick={handleImport}>
              导入
            </Button>
            <Button onClick={handleExport}>导出</Button>
          </Col>
        </Row>
      </Form>
      <div>
        <StandardTableNew
          rowKey="key"
          size="small"
          tableAlert={false}
          data={tableData}
          columns={columns}
          useEllipsis={true}
          isNotSelect={true}
          bordered={false}
          onChange={handleChange}
        />
        {addModalVisible && (
          <AddModal
            visible={addModalVisible}
            onCancel={() => {
              setAddModalVisible(false);
              editCurrentRow?.id && setEditCurrentRow({});
            }}
            onOk={handleAddOk}
            currentRow={currentRow}
            editCurrentRow={editCurrentRow}
          />
        )}
        {importModalVisible && (
          <ImportDeviceModal
            visible={importModalVisible}
            onCancel={() => setImportModalVisible(false)}
            onOk={handleImportOk}
            templateUrl={'/api/cmdb/ci/downMold'} // 可传模板下载链接
          />
        )}
      </div>
    </Modal>
  );
};

export default Form.create()(ConfigLineModal);
