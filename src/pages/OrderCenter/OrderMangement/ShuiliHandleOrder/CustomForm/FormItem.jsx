import React, { memo, PureComponent } from 'react';
import { Form, Input, Radio, Select, Checkbox, DatePicker, Row, Col } from 'antd';
import TextAreaHoc from './TextAreaHoc';
import InputHoc from './InputHoc';
import styles from './index.less';
import moment from 'moment';

class FormInput extends PureComponent {
  render() {
    return (
      <InputHoc {...this.props}>
        <Input size="small" spellCheck={false}></Input>
      </InputHoc>
    );
  }
}

class FormTextArea extends PureComponent {
  render() {
    return (
      <TextAreaHoc {...this.props}>
        <Input.TextArea size="small" autoSize spellCheck={false}></Input.TextArea>
      </TextAreaHoc>
    );
  }
}

class FormRadio extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Radio.Group {...rest}>
        {options.map(v => (
          <Radio value={v.value} key={v.value}>
            {v.label}
          </Radio>
        ))}
      </Radio.Group>
    );
  }
}

class FormCheckBox extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Checkbox.Group {...rest}>
        {options.map(v => (
          <Checkbox value={v.value} key={v.value}>
            {v.label}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  }
}

class FormSelect extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Select {...rest}>
        {options.map(v => (
          <Select.Option value={v.value} key={v.value}>
            {v.label}
          </Select.Option>
        ))}
      </Select>
    );
  }
}

class FormRangePicker extends PureComponent {
  state = {
    startValue: null,
    endValue: null,
  };

  // 禁用开始时间的日期
  disabledStartDate = (current) => {
    const { endValue } = this.state;
    if (!endValue) {
      return false;
    }
    // 开始时间不能晚于结束时间，且不能早于结束时间5天
    return current.valueOf() > endValue.valueOf() ||
           endValue.diff(current, 'hours') > 120;
  };

  // 禁用结束时间的日期
  disabledEndDate = (current) => {
    const { startValue } = this.state;
    if (!startValue) {
      return false;
    }
    // 结束时间不能早于开始时间，且不能晚于开始时间5天（120小时）
    return current.valueOf() <= startValue.valueOf() ||
           current.diff(startValue, 'hours') > 120;
  };

  // 禁用开始时间的时间
  disabledStartTime = (date) => {
    const { endValue } = this.state;
    if (!endValue || !date || !date.isSame(endValue, 'day')) {
      return {};
    }

    const endHour = endValue.hour();
    const endMinute = endValue.minute();

    return {
      disabledHours: () => {
        const hours = [];
        for (let i = endHour + 1; i < 24; i++) {
          hours.push(i);
        }
        return hours;
      },
      disabledMinutes: (selectedHour) => {
        if (selectedHour === endHour) {
          const minutes = [];
          for (let i = endMinute + 1; i < 60; i++) {
            minutes.push(i);
          }
          return minutes;
        }
        return [];
      },
    };
  };

  // 禁用结束时间的时间
  disabledEndTime = (date) => {
    const { startValue } = this.state;
    if (!startValue || !date) {
      return {};
    }

    // 如果是同一天，限制结束时间不能早于开始时间
    if (date.isSame(startValue, 'day')) {
      const startHour = startValue.hour();
      const startMinute = startValue.minute();

      return {
        disabledHours: () => {
          const hours = [];
          for (let i = 0; i < startHour; i++) {
            hours.push(i);
          }
          return hours;
        },
        disabledMinutes: (selectedHour) => {
          if (selectedHour === startHour) {
            const minutes = [];
            for (let i = 0; i < startMinute; i++) {
              minutes.push(i);
            }
            return minutes;
          }
          return [];
        },
      };
    }

    // 如果是第5天，限制时间不能超过开始时间+120小时
    const hoursDiff = date.diff(startValue, 'hours', true);
    if (hoursDiff >= 120) {
      const maxEndTime = moment(startValue).add(120, 'hours');
      if (date.isSame(maxEndTime, 'day')) {
        const maxHour = maxEndTime.hour();
        const maxMinute = maxEndTime.minute();

        return {
          disabledHours: () => {
            const hours = [];
            for (let i = maxHour + 1; i < 24; i++) {
              hours.push(i);
            }
            return hours;
          },
          disabledMinutes: (selectedHour) => {
            if (selectedHour === maxHour) {
              const minutes = [];
              for (let i = maxMinute + 1; i < 60; i++) {
                minutes.push(i);
              }
              return minutes;
            }
            return [];
          },
        };
      }
    }

    return {};
  };

  // 开始时间变化
  onStartChange = (value) => {
    this.setState({ startValue: value }, () => {
      this.triggerChange();
    });
  };

  // 结束时间变化
  onEndChange = (value) => {
    this.setState({ endValue: value }, () => {
      this.triggerChange();
    });
  };

  // 触发父组件的onChange
  triggerChange = () => {
    const { onChange } = this.props;
    const { startValue, endValue } = this.state;

    if (onChange) {
      if (startValue && endValue) {
        // 验证时间范围
        const diffInHours = endValue.diff(startValue, 'hours');
        if (diffInHours > 120) {
          console.warn('选择的时间范围不能超过5天（120小时）');
          return;
        }
        onChange([startValue, endValue]);
      } else {
        onChange([startValue, endValue]);
      }
    }
  };

  render() {
    const { startValue, endValue } = this.state;
    const { onChange, value, ...rest } = this.props;

    return (
      <Row gutter={8}>
        <Col span={12}>
          <DatePicker
            {...rest}
            value={startValue}
            placeholder="开始时间"
            format="YYYY-MM-DD HH"
            showTime={{
              format: 'HH',
              // defaultValue: moment('00:00', 'HH')
            }}
            disabledDate={this.disabledStartDate}
            disabledTime={this.disabledStartTime}
            onChange={this.onStartChange}
            style={{ width: '100%' }}
          />
        </Col>
        <Col span={12}>
          <DatePicker
            {...rest}
            value={endValue}
            placeholder="结束时间"
            format="YYYY-MM-DD HH"
            showTime={{
              format: 'HH',
              // defaultValue: moment('23', 'HH')
            }}
            disabledDate={this.disabledEndDate}
            disabledTime={this.disabledEndTime}
            onChange={this.onEndChange}
            style={{ width: '100%' }}
          />
        </Col>
      </Row>
    );
  }
}

const formControllMap = {
  input: FormInput,
  textarea: FormTextArea,
  radio: FormRadio,
  select: FormSelect,
  checkBox: FormCheckBox,
  rangePicker: FormRangePicker,
};

const FormItem = memo(props => {
  const {
    type,
    form,
    field,
    initialValue,
    required = true,
    label = '',
    formItemLayout = {},
    style = {},
    formItemClassName = '',
    rules = [],
    ...rest
  } = props;
  const { getFieldDecorator } = form;

  const FormControll = formControllMap[type];
  return (
    <Form.Item label={label} {...formItemLayout} className={styles[formItemClassName]}>
      {getFieldDecorator(field, {
        initialValue: initialValue,
        rules: [{ required: required, message: '必填' }, ...rules],
      })(<FormControll {...rest} style={style}></FormControll>)}
    </Form.Item>
  );
});

export { FormInput, FormTextArea, FormRadio, FormItem };
