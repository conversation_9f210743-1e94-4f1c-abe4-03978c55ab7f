import React, { Fragment, memo, useEffect, useImperativeHandle, useState, forwardRef } from 'react';
import { Button, message } from 'antd';
import request from '@/utils/request';
import CustomTable from '../CustomTable';
import { FormItem } from './FormItem';
import styles from './index.less';
import SignDateFormItem from './SignDateFormItem';
import moment from 'moment';

import { maxStrReg, chinesPattern, JsonParseString } from './utils';

const OrderCreate = forwardRef((props, ref) => {
  const { form, onAdd, onRemove, getAttrCode, type, dataSouce = [], record = {} } = props;
  const [businessSystemList, setBusinessSystemList] = useState([]);

  const isView = type === 'view' || type === 'handle';

  const defaultProps = type => {
    return { type, form, disabled: isView };
  };

  const getBusinessSystemList = async () => {
    const res = await request('/api/order/orderProcess/getWebOrInfoSysnameList');
    const list = (res.resData ?? []).map(v => ({ value: v, label: v }));
    setBusinessSystemList(list);
  };

  const getIrsBySystemName = async v => {
    const res = await request.post('/api/order/orderProcess/getIrsBySystemName', {
      data: {
        systemName: v,
      },
    });
    form.setFieldsValue({
      [getAttrCode('IRS应用编码')]: res.resData,
    });
  };

  const getSubmitParams = () => {
    return new Promise((resolve, reject) => {
      form.validateFields((errors, values) => {
        if (errors) {
          reject(errors);
          return;
        }
        const ecsIpCode = getAttrCode('ECS服务器IP');
        const bljAcount = getAttrCode('堡垒机账号');

        // 校验 ECS 服务器 IP
        const hasEcsIp = values[ecsIpCode]?.publicAdnPrivateIp || values[ecsIpCode]?.innovationIp;
        if (!hasEcsIp) {
          message.error('ECS服务器IP中公有云/专有云或信创云至少填写一个');
          reject(new Error('ECS服务器IP中公有云/专有云或信创云至少填写一个'));
          return;
        }

        // 校验堡垒机账号
        const hasBljAccount =
          values[bljAcount]?.publicAdnPrivateAccount || values[bljAcount]?.innovationAccount;
        if (!hasBljAccount) {
          message.error('堡垒机账号中公有云/专有云或信创云至少填写一个');
          reject(new Error('堡垒机账号中公有云/专有云或信创云至少填写一个'));
          return;
        }

        // 处理运维时间段数据，将 moment 对象转换为字符串
        const timeRangeCode = getAttrCode('运维时间段');
        if (values[timeRangeCode] && Array.isArray(values[timeRangeCode])) {
         const timeRange = values[timeRangeCode]?.filter(Boolean);
         if(timeRange.length === 2){
          // 转换为字符串格式保存
            values[timeRangeCode] = {
              startTime: timeRange[0]?.format('HH:mm'),
              endTime: timeRange[1]?.format('HH:mm')
            };
         }else{
          message.destroy()
          message.error('请选择运维时间范围');
         }
            
        }
        console.log('values', values);

        resolve(values);
      });
    });
  };

  useImperativeHandle(ref, () => ({
    getSubmitParams,
  }));

  useEffect(() => {
    getBusinessSystemList();
  }, []);

  const formConfig = {
    dataSouce,
    record,
    customColumns: [
      {
        label: '信息系统名称',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'systemName',
        required: true,

        render(text, record) {
          return (
            <FormItem
              {...defaultProps('select')}
              field={getAttrCode('信息系统名称')}
              initialValue={record[getAttrCode('信息系统名称')]}
              options={businessSystemList}
              showSearch
              filterOption={(input, option) =>
                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={getIrsBySystemName}
            />
          );
        },
      },
      {
        label: 'IRS应用编码',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'IRSCode',
        required: true,

        render(text, record) {
          return (
            <FormItem
              {...defaultProps('textarea')}
              field={getAttrCode('IRS应用编码')}
              initialValue={record[getAttrCode('IRS应用编码')]}
              disabled={true}
            />
          );
        },
      },
      {
        label: 'ECS服务器IP',
        labelCol: 6,
        rowSpan: 2,
        dataIndex: 'ecsIp',
        required: true,
        children: [
          {
            label: '公有云/专有云',
            labelCol: 4,
            valueCol: 14,
            dataIndex: 'ecsIp_gyy',
            required: false,

            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={`${getAttrCode('ECS服务器IP')}.publicAdnPrivateIp`}
                  initialValue={
                    JsonParseString(record[getAttrCode('ECS服务器IP')])?.publicAdnPrivateIp
                  }
                  required={false}
                />
              );
            },
          },
          {
            label: '信创云',
            labelCol: 4,
            valueCol: 14,
            dataIndex: 'ecsIp_xcy',
            required: false,
            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={`${getAttrCode('ECS服务器IP')}.innovationIp`}
                  initialValue={JsonParseString(record[getAttrCode('ECS服务器IP')])?.innovationIp}
                  required={false}
                />
              );
            },
          },
        ],
      },
      {
        label: '堡垒机账号',
        labelCol: 6,
        rowSpan: 2,
        dataIndex: 'blj',
        required: true,
        children: [
          {
            label: '公有云/专有云',
            labelCol: 4,
            valueCol: 14,
            dataIndex: 'blj_gyy',
            required: false,

            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={`${getAttrCode('堡垒机账号')}.publicAdnPrivateAccount`}
                  initialValue={
                    JsonParseString(record[getAttrCode('堡垒机账号')])?.publicAdnPrivateAccount
                  }
                  required={false}
                />
              );
            },
          },
          {
            label: '信创云',
            labelCol: 4,
            valueCol: 14,
            dataIndex: 'blj_xcy',
            required: false,

            render(text, record) {
              return (
                <FormItem
                  {...defaultProps('textarea')}
                  field={`${getAttrCode('堡垒机账号')}.innovationAccount`}
                  initialValue={
                    JsonParseString(record[getAttrCode('堡垒机账号')])?.innovationAccount
                  }
                  required={false}
                />
              );
            },
          },
        ],
      },
      {
        label: '运维时间段',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'runTimeRang',
        required: true,
        render(text, record) {
          // 解析已有的时间范围数据
          let initialValue = null;
      

          return (
            <FormItem
              {...defaultProps('rangePicker')}
              field={getAttrCode('运维时间段')}
              initialValue={initialValue}
            />
          );
        },
      },
      {
        label: '更新升级内容',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'updateContent',
        required: true,
        rowHeight: 140,
        render(text, recor) {
          return (
            <FormItem
              {...defaultProps('textarea')}
              field={getAttrCode('更新升级内容')}
              initialValue={record[getAttrCode('更新升级内容')]}
              rules={maxStrReg(200)}
            />
          );
        },
      },

      {
        label: '开发运维单位确认',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'developerUnit',
        required: false,
        rowHeight: 140,
        render(text, record) {
          const currentRow = JsonParseString(record[getAttrCode('开发运维单位确认')]);
          return (
            <Fragment>
              <FormItem
                {...defaultProps('textarea')}
                field={`${getAttrCode('开发运维单位确认')}.content`}
                initialValue={
                  '已对本次更新发布的系统代码，安装升级的插件、中间件等第三方软件进行全面安全排查，确认不存在中、高危安全漏洞。'
                }
                style={{ textAlign: 'left' }}
                disabled={true}
              />

              <div style={{ marginTop: '16px' }}>
                <div style={{ width: '50%', display: 'inline-block' }}>
                  <FormItem
                    label="开发运维人员（签字）"
                    {...defaultProps('textarea')}
                    // field={getAttrCode('开发运维人员')}

                    field={`${getAttrCode('开发运维单位确认')}.sign`}
                    initialValue={currentRow?.sign}
                    required={false}
                    formItemLayout={{ labelCol: { span: 12 }, wrapperCol: { span: 12 } }}
                    formItemClassName={'leftLableFormItem'}
                  />
                </div>
                <div style={{ width: '50%', display: 'inline-block' }}>
                  <FormItem
                    label="联系电话"
                    {...defaultProps('textarea')}
                    formItemLayout={{ labelCol: { span: 4, offset: 2 }, wrapperCol: { span: 18 } }}
                    field={`${getAttrCode('开发运维单位确认')}.phone`}
                    initialValue={currentRow?.phone}
                    required={false}
                    formItemClassName={'leftLableFormItem'}
                  />
                </div>
              </div>
              <FormItem
                label="单位名称"
                {...defaultProps('textarea')}
                formItemLayout={{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }}
                field={`${getAttrCode('开发运维单位确认')}.unitName`}
                initialValue={currentRow?.unitName}
                required={false}
                formItemClassName={'leftLableFormItem'}
              />
              <SignDateFormItem
                record={record}
                defaultProps={defaultProps}
                getAttrCode={getAttrCode}
                style={{ justifyContent: 'flex-end', alignItems: 'center' }}
                field={getAttrCode('开发运维单位确认')}
              />
            </Fragment>
          );
        },
      },
      {
        label: '系统责任单位确认',
        labelCol: 6,
        valueCol: 18,
        dataIndex: 'opreationUnit',
        required: false,
        rowHeight: 140,
        render(text, record) {
          const currentRow = JsonParseString(record[getAttrCode('系统责任单位确认')]);
          return (
            <Fragment>
              <FormItem
                label="应用管理员（签字）"
                {...defaultProps('textarea')}
                formItemLayout={{ labelCol: { span: 5 }, wrapperCol: { span: 19 } }}
                // field={getAttrCode('应用管理员')}
                field={`${getAttrCode('系统责任单位确认')}.sign`}
                initialValue={currentRow?.sign}
                required={false}
                formItemClassName={'leftLableFormItem'}
              />
              <FormItem
                label="处室（单位）名称"
                {...defaultProps('textarea')}
                formItemLayout={{ labelCol: { span: 5 }, wrapperCol: { span: 19 } }}
                field={`${getAttrCode('系统责任单位确认')}.unit`}
                initialValue={currentRow?.unit}
                required={false}
                formItemClassName={'leftLableFormItem'}
              />
              <SignDateFormItem
                record={record}
                defaultProps={defaultProps}
                getAttrCode={getAttrCode}
                style={{ justifyContent: 'flex-end', alignItems: 'center' }}
                field={getAttrCode('系统责任单位确认')}
              />
            </Fragment>
          );
        },
      },
    ],
  };

  return (
    <div className={styles.customForm}>
      <div className={styles.title}>政务云ECS服务器更新升级安全检查确认单</div>
      <CustomTable
        config={formConfig}
        style={{ paddingTop: 16 }}
        rowHeight={70}
        headHeight={70}
      ></CustomTable>
    </div>
  );
});

export default memo(OrderCreate);
