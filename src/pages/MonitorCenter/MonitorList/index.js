/*
 * @LastEditors: xuHuiHui
 * @LastEditTime: 2020-11-12 14:20:03
 * @FilePath: /cloudPlatform/src/pages/MonitorCenter/MonitorList/index.js
 */
import React, { Component } from 'react';
import { connect } from 'dryad';
import { Form, Tabs } from 'antd';
import { Card } from '@/components/PAntd';
import styles from './index.less';
import NavTabs from '@/assets/navTabs.png';
import AlarmList from '@/pages/MonitorCenter/MonitorList/alarmList';
import NomatchAlarm from '@/pages/MonitorCenter/MonitorList/NomatchAlarm'; 

const { TabPane } = Tabs;

@connect(({ monitorList, monitorRule, loading }) => ({
  monitorList,
  monitorRule,
  loading: loading.effects['monitorList/getAlarmList'],
}))
@Form.create()
export default class MonitorList extends Component {
  state = {
    types: this.props.location?.query?.types || '未恢复',
    defaultActiveKey: this.props.location?.query?.defaultActiveKey || '1',
  };
  componentDidUpdate(preProps) {
    if (this.props.location?.query?.types !== preProps.location?.query?.types) {
      this.setState({ types: this.props.location?.query?.types });
    }
  }
  callback = key => {
    this.setState({
      types: key == '1' ? '未恢复' : key == '2' ? '已恢复' : key == '3' ? '全部' : '未匹配告警',
    });
  };

  render() {
    const { types, defaultActiveKey } = this.state;
    // const tabs=[
    //   {
    //     name: '未恢复告警',
    //     tabPane: <AlarmList query={this.props.location.query} types={types} />,
    //     color: '#F9758A',
    //   },
    // ]
    return (
      <div className={styles.panClass}>
        <Card style={{ height: 120 }}>
          <Tabs defaultActiveKey={defaultActiveKey} onChange={this.callback}>
            <TabPane
              tab={
                <span className={styles.navSpan}>
                  <div className={styles.navTitle} style={{ background: '#F9758A' }}>
                    <img src={NavTabs} alt="" />
                  </div>
                  <div>未恢复告警</div>
                </span>
              }
              key="1"
            >
              {types === '未恢复' ? (
                <AlarmList query={this.props.location.query} types={types} />
              ) : null}
            </TabPane>
            <TabPane
              tab={
                <span className={styles.navSpan}>
                  <div className={styles.navTitle} style={{ background: '#38DFC5' }}>
                    <img src={NavTabs} alt="" />
                  </div>
                  <div>已恢复告警</div>
                </span>
              }
              key="2"
            >
              {types === '已恢复' ? (
                <AlarmList query={this.props.location.query} types={types} />
              ) : null}
            </TabPane>
            <TabPane
              tab={
                <span className={styles.navSpan}>
                  <div className={styles.navTitle} style={{ background: '#5C76FB' }}>
                    <img src={NavTabs} alt="" />
                  </div>
                  <div>全部告警</div>
                </span>
              }
              key="3"
            >
              {types === '全部' ? (
                <AlarmList query={this.props.location.query} types={types} />
              ) : null}
            </TabPane>
            <TabPane
              tab={
                <span className={styles.navSpan}>
                  <div className={styles.navTitle} style={{ background: '#FCC034' }}>
                    <img src={NavTabs} alt="" />
                  </div>
                  <div>未匹配告警</div>
                </span>
              }
              key="4"
            >
              {types === '未匹配告警' ? (
                <NomatchAlarm query={this.props.location.query} types={types} />
              ) : null}
            </TabPane>
          </Tabs>
        </Card>
      </div>
    );
  }
}
