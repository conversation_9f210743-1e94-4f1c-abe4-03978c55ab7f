/*
 * @LastEditors: @LastEditedBy
 * @LastEditTime: 2025-08-04 15:07:17
 * @FilePath: /水利厅运维/src/pages/MonitorCenter/MonitorList/alarmList.js
 */
import React, { Component, Fragment } from 'react';
import { connect } from 'dryad';
import {
  Button,
  Row,
  Col,
  Form,
  DatePicker,
  Input,
  Select,
  Tag,
  Modal,
  Descriptions,
  Tabs,
  message,
  Divider,
} from 'antd';
import moment from 'moment';
import { Card } from '@/components/PAntd';
import StandardTable from '@/components/StandardTableNew';
import { goPage } from '@/utils/openTab';
import styles from './index.less';
import Ellipsis from '@/components/Ellipsis';
import Change from '@/assets/icons/change.png';
import getColumnWidth from '@/utils/getColumnWidth';
import { commonEllipsis } from '@/utils/commonEllipsis';
import AlarmRemark from './AlarmRemark';

const { RangePicker } = DatePicker;
const FormItem = Form.Item;
const { Option } = Select;

const defaultPagination = {
  pageSize: 10,
  current: 1,
};
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
let Timer;
@connect(({ monitorList, monitorRule, user, loading }) => ({
  monitorList,
  monitorRule,
  user,
  loading: loading.effects['monitorList/getAlarmList'],
}))
@Form.create()
export default class AlarmList extends Component {
  state = {
    pagination: defaultPagination,
    selectedRows: [],
    selectedRowKeys: [],
    visible: false,
    record: {},
    selectData: [],
    sorter: {},
  };

  componentDidMount() {
    const {
      dispatch,
      query,
      user: {
        currentUser: { userId },
      },
    } = this.props;
    this.getSeleteData();
    dispatch({
      type: 'monitorRule/getAlarmTypeList',
      payload: {
        current: 0,
        pageSize: 999,
      },
    });
    this.setState({ userId }, () => {
      if (query) {
        this.getData();
      } else {
        this.initData();
      }
    });
  }

  componentWillUnmount() {
    // clearInterval(Timer)
  }

  getSeleteData = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'monitorList/getDeviceType',
      callback: data => {
        if (data.code != 200) {
          message.error(data.msg);
          return;
        }
        this.setState({
          selectData: data.resData,
        });
      },
    });
  };

  componentDidUpdate(preProps) {
    if (JSON.stringify(preProps.query) !== JSON.stringify(this.props.query)) {
      this.getData();
    } else if (JSON.stringify(preProps.types) !== JSON.stringify(this.props.types)) {
      this.getData();
    }
  }
  getData = () => {
    const { query = {}, form, types = '未恢复' } = this.props;
    let param = {};
    const { ip = null, dateTime = [], deviceType = null } = query;
    param.deviceIp = ip;

    if (dateTime.length > 0) {
      const [start, end] = dateTime;
      param.startTime = [moment(start + ' 00:00:00'), moment(end + ' 24:00:00')];
    }
    if (types == '未恢复') {
      param.deviceType = deviceType;
    }
    form.setFieldsValue(param, () => {
      this.handleSearch();
    });
  };

  request = async rawParams => {
    try {
      const { current, pageSize, ...other } = rawParams;
      const params = { ...other, start: (current - 1) * pageSize, length: pageSize };
      const response = await getUserGroups(params);
      return Promise.resolve({
        data: response.data,
        success: true,
        total: response.iTotalRecords,
      });
    } catch {
      return Promise.resolve({
        data: [],
        success: false,
      });
    }
  };
  initData = (params = {}) => {
    const { dispatch, form, types } = this.props;
    const { searchValue = {}, sorter = {}, remarkType, focusType, userId } = this.state;
    const { pagination = {}, ...restParams } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };
    const { alarmType, alarmLevel, startTime, updateTime, endTime, ...restValues } = searchValue;
    const format = 'YYYY-MM-DD HH:mm:00';
    dispatch({
      type: 'monitorList/getAlarmList',
      payload: {
        ...newPagination,
        ...restValues,
        alarmLevel: alarmLevel ? alarmLevel.join(',') : '',
        alarmType: alarmType ? alarmType.join(',') : '',
        startTime: startTime ? startTime.map(v => v.format(format)).join(',') : '',
        updateTime: updateTime ? updateTime.map(v => v.format(format)).join(',') : '',
        endTime: endTime ? endTime.map(v => v.format(format)).join(',') : '',
        ...restParams,

        // userId,
        remarkType: remarkType == 'primary' ? true : '',
        followType: focusType == 'primary' ? true : '',

        alarmStatus: types === '未恢复' ? 'UNKNOWN' : types === '已恢复' ? 'CLEAR' : '',
        sidx: sorter.field,
        sord: sorter.order === 'ascend' ? 'asc' : 'desc',
      },
    });
    this.setState({ pagination: newPagination, selectedRows: [] });
  };
  handleSearch = () => {
    const { form } = this.props;
    form.validateFieldsAndScroll((err, values) => {
      if (err) return;
      this.setState({ searchValue: { ...values } }, () => {
        this.initData();
      });
    });
  };

  handleSelectRows = (selectedRowKeys, rows) => {
    this.setState({
      selectedRows: selectedRowKeys,
      selectedRowKeys,
    });
  };

  handlePaginationTable = (pagination, filtersArg, sorter) => {
    this.setState({ sorter }, () => {
      this.initData({ pagination });
    });
  };

  handleFormReset = () => {
    const { form } = this.props;
    form.resetFields();
    this.setState({ searchValue: {} }, () => {
      this.initData();
    });
  };

  handleRecovery = () => {
    const { selectedRows } = this.state;
    const { dispatch } = this.props;
    Modal.confirm({
      title: '恢复确认',
      content: `是否恢复${selectedRows.length}条?`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dispatch({
          type: 'monitorList/recoveryAlarm',
          payload: {
            ids: selectedRows.map(v => v.alarmId),
          },
          callback: () => {
            this.initData();
          },
        });
      },
    });
  };

  handleExport = () => {
    const { dispatch, form, types } = this.props;
    const { searchValue, remarkType, focusType } = this.state;
    const {
      alarmId,
      alarmTitle,
      deviceType,
      deviceIp,
      alarmType,
      alarmLevel,
      startTime,
      updateTime,
      endTime,
      ...restValues
    } = searchValue;
    const format = 'YYYY-MM-DD HH:mm:00';
    dispatch({
      type: 'monitorList/exportAlarmExcel',
      payload: {
        // ...restValues,
        alarmId: alarmId ? alarmId : '',
        alarmTitle: alarmTitle ? alarmTitle : '',
        deviceIp: deviceIp ? deviceIp : '',
        deviceType: deviceType ? deviceType : '',
        alarmLevel: alarmLevel ? alarmLevel.join(',') : '',
        alarmType: alarmType ? alarmType.join(',') : '',
        startTime: startTime ? startTime.map(v => v.format(format)).join(',') : '',
        updateTime: updateTime ? updateTime.map(v => v.format(format)).join(',') : '',
        endTime: endTime ? endTime.map(v => v.format(format)).join(',') : '',
        alarmStatus: types === '未恢复' ? 'UNKNOWN' : types === '已恢复' ? 'CLEAR' : '',

        remarkType: remarkType == 'primary' ? true : '',
        followType: focusType == 'primary' ? true : '',
      },
    });
  };
  searchOwnRemark = () => {
    const { remarkType = 'default' } = this.state;
    this.setState({ remarkType: remarkType == 'default' ? 'primary' : 'default' }, () => {
      this.initData();
    });
  };
  searchOwnFocus = () => {
    const { focusType = 'default' } = this.state;
    this.setState({ focusType: focusType == 'default' ? 'primary' : 'default' }, () => {
      this.initData();
    });
  };
  handleFocus = record => {
    const { dispatch } = this.props;
    const { pagination } = this.state;
    if (record.followType) {
      Modal.confirm({
        title: '取消确认',
        content: '确认要取消关注此条告警吗？',
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
          dispatch({
            type: 'monitorList/deleteFollow',
            payload: {
              alarmId: record.alarmId,
            },
            callback: res => {
              if (res.code == '200') {
                message.success('取消关注成功');
                this.initData({ pagination });
              } else {
                message.error(res.msg);
              }
            },
          });
        },
      });
    } else {
      dispatch({
        type: 'monitorList/addFollow',
        payload: {
          alarmId: record.alarmId,
        },
        callback: res => {
          if (res.code == '200') {
            message.success('关注成功');
            this.initData({ pagination });
          } else {
            message.error(res.msg);
          }
        },
      });
    }
  };
  handleClickDetail = record => {
    const { dispatch } = this.props;
    dispatch({
      type: 'monitorList/getAlarmDetailInfo',
      payload: {
        alarmId: record.alarmId,
      },
    });

    dispatch({
      type: 'monitorList/getDeviceInfoByModuleTypeCiId',
      payload: {
        moduleType: record.moduleType,
        ciId: record.ciId,
      },
    });

    this.setState({ visible: true, record });
  };
  callback = key => { };
  render() {
    const {
      pagination,
      selectedRows,
      visible,
      record,
      selectData,
      selectedRowKeys,
      remarkType = 'default',
      focusType = 'default',
      remarkVisible = false,
      remarkInfo = {},
    } = this.state;
    const {
      form: { getFieldDecorator },
      monitorList: {
        alarmList: { total = 0, data = [] },
        alarmDetailInfo = {},
        deviceInfoByModuleTypeCiId,
      },
      monitorRule: { alarmTypeList = {} },
      loading,
      types,
    } = this.props;

    const renderNull = text => <span>{text || '--'}</span>;
    const columns = [

      {
        title: '资源名称',
        dataIndex: 'deviceName',
        width: 120,
        key: 'deviceName',
        align: 'center',
        esllipsis: true,
        render: commonEllipsis
      },
      {
        title: 'URL',
        dataIndex: 'url',
        width: 180,
        key: 'url',
        align: 'center',
        esllipsis: true,
        render: commonEllipsis
        // sorter: true,
        // render: renderNull,
      },
      // {
      //   title: '告警值',
      //   dataIndex: 'alarmValue',
      //   width: 160,
      //   key: 'alarmValue',
      //   align: 'center',
      //   render: data => {
      //     return data?(
      //       <Ellipsis tooltip length={20}>
      //         {data}
      //       </Ellipsis>
      //     ):'--';
      //   },
      // },
      {
        dataIndex: 'alarmTitle',
        title: '告警标题',
        key: 'alarmTitle',
        width: 120,
        align: 'center',
        sorter: true,
        esllipsis: true,
        render: commonEllipsis
        // render: (text, record) => {
        //   // text='虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试虚拟机cpu过高测试'
        //   if (record.shieldPoint == 1) {
        //     return (
        //       <div>
        //         <img
        //           src={Change}
        //           alt=""
        //           style={{
        //             margin: '0 10px 0 0',
        //             width: '20px',
        //             height: '20px',
        //             display: 'inline-block',
        //           }}
        //         />
        //         {text}
        //       </div>
        //     );
        //   } else {
        //     return text || '--';
        //   }
        // },
      },
      {
        dataIndex: 'alarmStatus',
        title: '告警状态',
        key: 'alarmStatus',
        width: 120,
        sorter: true,
        render: text => {
          const color = text === 'CLEAR' ? 'grey' : 'red';
          return <span style={{ color }}>{text === 'CLEAR' ? '已恢复' : '未恢复'}</span>;
        },
      },

      {
        dataIndex: 'deviceType',
        title: '资源类型',
        width: 120,
        sorter: true,
        key: 'deviceType',
        esllipsis: true,
        render: commonEllipsis
      },

      {
        dataIndex: 'deviceIp',
        title: '资源IP',
        width: 180,
        sorter: true,
        key: 'deviceIp',
        esllipsis: true,
        render: commonEllipsis
        // render: (data) => {
        //   return (<Ellipsis tooltip length={7}>{data}</Ellipsis>)
        // },
      },
      // {
      //   dataIndex: 'ciId',
      //   title: '配置项编号',
      //   width: 150,
      //   key: 'ciId',
      //   render: renderNull,
      // },
      {
        dataIndex: 'startTime',
        title: '告警发生时间',
        key: 'startTime',
        sorter: true,
        width: 150,
        esllipsis: true,
        render: commonEllipsis
        // render: text => <span>{text ? text.split('.')[0] : '--'}</span>,
      },
      {
        dataIndex: 'updateTime',
        title: '告警更新时间',
        key: 'updateTime',
        sorter: true,
             width: 150,
        esllipsis: true,
        render: commonEllipsis
        // render: text => <span>{text ? text.split('.')[0] : '--'}</span>,
      },
      {
        dataIndex: 'endTime',
        title: '告警结束时间',
        key: 'endTime',
        sorter: true,
             width: 150,
        esllipsis: true,
        render: commonEllipsis
        // render: text => <span>{text ? text.split('.')[0] : '--'}</span>,
      },
      {
        dataIndex: 'alarmId',
        title: '告警ID',
        width: 120,
        key: 'alarmId',
        sorter: true,
        esllipsis: true,
        render: commonEllipsis
      },
      {
        dataIndex: 'alarmLevel',
        title: '告警等级',
        key: 'alarmLevel',
        width: 150,
        sorter: true,
        render: text => {
          if (!text) return <span>--</span>;
          const tags = {
            一级告警: 'red',
            二级告警: 'volcano',
            三级告警: 'orange',
            四级告警: 'blue',
          };
          return <Tag color={tags[text]}>{text}</Tag>;
        },
      },
      {
        dataIndex: 'alarmType',
        title: '告警分类',
        width: 180,
        sorter: true,
        key: 'alarmType',
        esllipsis: true,
        render: commonEllipsis
        // render: (data) => {
        //   return (<Ellipsis tooltip length={10}>{data}</Ellipsis>)
        // },
      },
      {
        dataIndex: 'action',
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 220,
        // sorter: true,
        align: 'center',
        render: (text, record) => {
          return (
            <Fragment>
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.handleClickDetail(record);
                }}
              >
                查看详情
              </a>
              <Divider type="vertical" />
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.setState({ remarkInfo: record, remarkVisible: true });
                }}
              >
                评注
              </a>
              <Divider type="vertical" />
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.handleFocus(record);
                }}
              >
                {record.followType ? '取消关注' : '关注'}
              </a>
            </Fragment>
          );
        },
      },
    ];
    const columns1 = [
      {
        title: '资源名称',
        dataIndex: 'deviceName',
        key: 'deviceName',
        width: '8%',
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
      },
      {
        title: 'URL',
        dataIndex: 'url',
        key: 'url',
        width: '12%',
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
      },
      // {
      //   title: '告警值',
      //   dataIndex: 'alarmValue',
      //   width: 160,
      //   key: 'alarmValue',
      //   align: 'center',
      //   render: data => {
      //     return data?(
      //       <Ellipsis tooltip length={20}>
      //         {data}
      //       </Ellipsis>
      //     ):'--';
      //   },
      // },
      {
        dataIndex: 'alarmTitle',
        title: '告警标题',
        key: 'alarmTitle',
        width: '15%',
        sorter: true,
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
        // render: (text, record) => {
        //   if (record.shieldPoint == 1) {
        //     return (
        //       <div>
        //         <img
        //           src={Change}
        //           alt=""
        //           style={{
        //             margin: '0 10px 0 0',
        //             width: '20px',
        //             height: '20px',
        //             display: 'inline-block',
        //           }}
        //         />
        //         {text}
        //       </div>
        //     );
        //   } else {
        //     // return <Ellipsis tooltip length={10} >{text || '--'}</Ellipsis>
        //     return text || '--';
        //   }
        // },
      },

      {
        dataIndex: 'alarmStatus',
        title: '告警状态',
        key: 'alarmStatus',
        width: '8%',
        sorter: true,
        align: 'center',
        render: text => {
          const color = text === 'CLEAR' ? 'grey' : 'red';
          return <span style={{ color }}>{text === 'CLEAR' ? '已恢复' : '未恢复'}</span>;
        },
      },

      {
        dataIndex: 'deviceType',
        title: '资源类型',
        width: '8%',
        sorter: true,
        align: 'center',
        key: 'deviceType',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
      },

      {
        dataIndex: 'deviceIp',
        title: '资源IP',
        width: '10%',
        sorter: true,
        align: 'center',
        key: 'deviceIp',
        render: renderNull,
      },
      // {
      //   dataIndex: 'ciId',
      //   title: '配置项编号',
      //   width: 150,
      //   key: 'ciId',
      //   render: renderNull,
      // },
      {
        dataIndex: 'startTime',
        title: '告警发生时间',
        key: 'startTime',
        width: '12%',
        sorter: true,
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
        // render: text => <span>{text ? text.split('.')[0] : '--'}</span>,
      },
      {
        dataIndex: 'updateTime',
        title: '告警更新时间',
        key: 'updateTime',
        width: '12%',
        sorter: true,
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
        // render: text => <span>{text ? text.split('.')[0] : '--'}</span>,
      },
      // {
      //   dataIndex: 'endTime',
      //   title: '告警结束时间',
      //   key: 'endTime',
      //   render: text => <span>{text ? text.split('.')[0] : '--'}</span>,
      // },
      {
        dataIndex: 'alarmId',
        title: '告警ID',
        key: 'alarmId',
        width: '8%',
        align: 'center',
        sorter: true,
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
        // render: renderNull,
      },
      {
        dataIndex: 'alarmLevel',
        title: '告警等级',
        key: 'alarmLevel',
        width: '8%',
        // showSorterTooltip:false,
        // sortDirections: ['descend', 'ascend'],
        sorter: true,
        align: 'center',
        ellipsis: {
          showTitle: false,
        },
        render: text => {
          if (!text) return <span>--</span>;
          const tags = {
            一级告警: 'red',
            二级告警: 'volcano',
            三级告警: 'orange',
            四级告警: 'blue',
          };
          return <Tag color={tags[text]}>{text}</Tag>;
        },
      },
      {
        dataIndex: 'alarmType',
        title: '告警分类',
        width: '8%',
        sorter: true,
        align: 'center',
        key: 'alarmType',
        ellipsis: {
          showTitle: false,
        },
        render: commonEllipsis
        // render: (data) => {
        //   return (<Ellipsis tooltip length={10}>{data}</Ellipsis>)
        // },
      },
      {
        dataIndex: 'action',
        title: '操作',
        key: 'action',
        fixed: 'right',
        align: 'center',
        width: '11%',
        render: (_, record) => {
          return (
            <Fragment>
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.handleClickDetail(record);
                }}
              >
                查看详情
              </a>
              <Divider type="vertical" />
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  goPage(`/resourceCenter/deviceMonitorDetail/${record.ciId}`, {
                    pname: '',
                    modelName: record.deviceType,
                    ciId: record.ciId,
                    moduleType: record.moduleType,
                  });
                }}
              >
                监控详情
              </a>
              <Divider type="vertical" />
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.setState({ remarkInfo: record, remarkVisible: true });
                }}
              >
                评注
              </a>
              <Divider type="vertical" />
              <a
                onClick={(e) => {
                  e.stopPropagation();
                  this.handleFocus(record);
                }}
              >
                {record.followType ? '取消关注' : '关注'}
              </a>
            </Fragment>
          );
        },
      },
    ];

    let start;
    const rangePicker = {
      disabledDate(current) {
        const isMoreThanToday = current > moment().endOf('day');
        if (start && current) {
          const isMoreThanStart = current.diff(start, 'months') > 12;
          const isMoreThanEnd = start.diff(current, 'months') > 12; // 保证选定起始日期后，只能选择某段时间内作为结束日期
          return isMoreThanStart || isMoreThanEnd || isMoreThanToday;
        }
        return isMoreThanToday;
      },
      onCalendarChange(dates) {
        if (dates.length === 1) {
          start = dates[0];
        } else {
          start = null;
        }
      },
      onOpenChange() {
        start = null;
      },
    };

    return (
      <div>
        <Card style={{ marginBottom: '10px' }}>
          <Form className={styles.monitorList} {...formItemLayout}>
            <Row gutter={{ md: 4, lg: 12, xl: 24 }} style={{ marginTop: '8px' }}>
              <Col span={8}>
                <FormItem label="告警ID">
                  {getFieldDecorator('alarmId')(<Input placeholder="请输入" allowClear />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="告警标题">
                  {getFieldDecorator('alarmTitle')(<Input placeholder="请输入" allowClear />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="资源名称">
                  {getFieldDecorator('deviceName')(<Input placeholder="请输入" allowClear />)}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="告警等级">
                  {getFieldDecorator('alarmLevel')(
                    <Select
                      placeholder="请选择告警等级"
                      allowClear
                      showSearch
                      mode="multiple"
                      maxTagCount={1}
                      maxTagTextLength={1}
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      <Option value="一级告警">一级告警</Option>
                      <Option value="二级告警">二级告警</Option>
                      <Option value="三级告警">三级告警</Option>
                      <Option value="四级告警">四级告警</Option>
                    </Select>,
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="资源类型">
                  {getFieldDecorator('deviceType')(
                    <Select
                      placeholder="请选择资源类型"
                      allowClear
                    // showSearch
                    // filterOption={(input, option) =>
                    //   option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    // }
                    >
                      {selectData.map(v => (
                        <Option key={v.value} value={v.value}>
                          {v.name}
                        </Option>
                      ))}
                    </Select>,
                  )}
                </FormItem>
              </Col>
              {/* </Row>
            <Row gutter={{ md: 4, lg: 12, xl: 24 }}> */}
              <Col span={8}>
                <FormItem label="告警分类">
                  {getFieldDecorator('alarmType')(
                    <Select
                      placeholder="请选择"
                      allowClear
                      showSearch
                      mode="multiple"
                      maxTagCount={1}
                      maxTagTextLength={1}
                      filterOption={(input, option) =>
                        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    >
                      {alarmTypeList.data &&
                        alarmTypeList.data.map(v => (
                          <Option key={v.id} value={v.alarmType}>
                            {v.alarmType}
                          </Option>
                        ))}
                    </Select>,
                  )}
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem label="资源IP">
                  {getFieldDecorator('deviceIp')(<Input placeholder="请输入" allowClear />)}
                </FormItem>
              </Col>
              {/*<Col span={6}>*/}
              {/*  <FormItem label="配置项编号">*/}
              {/*    {getFieldDecorator('ciId')(<Input placeholder="请输入" allowClear />)}*/}
              {/*  </FormItem>*/}
              {/*</Col>*/}
              <Col span={8}>
                <FormItem label="告警发生时间">
                  {getFieldDecorator(
                    'startTime',
                    {},
                  )(
                    <RangePicker
                      style={{ width: '100%' }}
                      showTime={{ format: 'HH:mm' }}
                      format="YYYY-MM-DD HH:mm"
                      {...rangePicker}
                    />,
                  )}
                </FormItem>
              </Col>
              {types != '未恢复' ? (
                <Col span={8}>
                  <FormItem label="告警更新时间">
                    {getFieldDecorator('updateTime')(
                      <RangePicker
                        style={{ width: '100%' }}
                        showTime={{ format: 'HH:mm' }}
                        format="YYYY-MM-DD HH:mm"
                        {...rangePicker}
                      />,
                    )}
                  </FormItem>
                </Col>
              ) : null}

              {types != '未恢复' ? (
                <Col span={8}>
                  <FormItem label="告警结束时间">
                    {getFieldDecorator('endTime')(
                      <RangePicker
                        style={{ width: '100%' }}
                        showTime={{ format: 'HH:mm' }}
                        format="YYYY-MM-DD HH:mm"
                        {...rangePicker}
                      />,
                    )}
                  </FormItem>
                </Col>
              ) : null}
              {/* </Row>

            <Row gutter={{ md: 4, lg: 12, xl: 24 }}> */}
              <Col
                span={types == '未恢复' ? 8 : 16}
                style={{ textAlign: 'right', marginTop: '4px' }}
              >
                <span>
                  <Button
                    type="primary"
                    onClick={() => {
                      this.handleSearch();
                    }}
                  >
                    查询
                  </Button>
                  <Button style={{ marginLeft: 8 }} onClick={this.handleFormReset}>
                    重置
                  </Button>
                </span>
              </Col>
            </Row>
          </Form>
        </Card>

        <Card title="告警列表">
          {/* <Button
            onClick={() => {
              this.handleExport();
            }}
            style={{ marginBottom: '10px', marginRight:'10px' }}
          >
            导出
          </Button>
          {
            types === '未恢复' ? <Button
              disabled={selectedRows?.length === 0}
              onClick={() => {
                this.handleRecovery();
              }}
              style={{ marginBottom: '10px' }}
            >
              手动恢复
            </Button> : null
          } */}
          <div style={{ height: 'auto' }} className={styles.standTable}>
            <StandardTable
              buttonList={
                types === '未恢复'
                  ? [
                    {
                      text: '我的评注',
                      type: remarkType,
                      isGhost: false,
                      features: this.searchOwnRemark,
                      icon: 'message',
                      style: { color: remarkType == 'primary' ? '#fff' : 'rgba(0, 0, 0, 0.65)' },
                    },
                    {
                      text: '我的关注',
                      type: focusType,
                      isGhost: false,
                      features: this.searchOwnFocus,
                      icon: 'star',
                      style: { color: focusType == 'primary' ? '#fff' : 'rgba(0, 0, 0, 0.65)' },
                    },
                    { text: '导出', type: 'primary', isGhost: true, features: this.handleExport },
                    {
                      text: '手动恢复',
                      type: 'default',
                      disabled: selectedRows?.length === 0,
                      features: this.handleRecovery,
                    },
                  ]
                  : types === '已恢复' || types == '全部'
                    ? [
                      {
                        text: '我的评注',
                        type: remarkType,
                        isGhost: false,
                        features: this.searchOwnRemark,
                        icon: 'message',
                        style: { color: remarkType == 'primary' ? '#fff' : 'rgba(0, 0, 0, 0.65)' },
                      },
                      {
                        text: '我的关注',
                        type: focusType,
                        isGhost: false,
                        features: this.searchOwnFocus,
                        icon: 'star',
                        style: { color: focusType == 'primary' ? '#fff' : 'rgba(0, 0, 0, 0.65)' },
                      },
                      { text: '导出', type: 'primary', isGhost: true, features: this.handleExport },
                    ]
                    : [{ text: '导出', type: 'primary', isGhost: true, features: this.handleExport }]
              }
              tools={true}
              loading={loading}
              rowKey="alarmId"
              data={{ list: data, pagination: { ...pagination, total } }}
              columns={types === '未恢复' ? columns1 : columns}
              selectedRows={selectedRows}
              onSelectRow={this.handleSelectRows}
              onChange={this.handlePaginationTable}
              size={'small'}
              sccroll={{ x: '100%' }}
              useEllipsis={true}
            />
          </div>
        </Card>

        <Modal
          title="告警详情"
          visible={visible}
          onCancel={() => {
            this.setState({ visible: false });
          }}
          footer={null}
          width={700}
        >
          <Row className={styles.descriptions}>
            <Col span={24}>
              <Descriptions column={2}>
                <Descriptions.Item label="告警ID">
                  {alarmDetailInfo.alarmId || '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警标题">
                  {alarmDetailInfo.alarmTitle || '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警状态">
                  {alarmDetailInfo
                    ? alarmDetailInfo.alarmStatus === 'CLEAR'
                      ? '已恢复'
                      : '未恢复'
                    : '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警等级">
                  {alarmDetailInfo.alarmLevel || '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警分类">
                  {alarmDetailInfo.alarmType || '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警发生时间">
                  {alarmDetailInfo.startTime
                    ? moment.unix(alarmDetailInfo.startTime / 1000).format('YYYY-MM-DD HH:mm:ss')
                    : '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警更新时间">
                  {alarmDetailInfo.updateTime
                    ? moment.unix(alarmDetailInfo.updateTime / 1000).format('YYYY-MM-DD HH:mm:ss')
                    : '--'}
                </Descriptions.Item>
                <Descriptions.Item label="告警恢复时间">
                  {alarmDetailInfo.endTime
                    ? moment.unix(alarmDetailInfo.endTime / 1000).format('YYYY-MM-DD HH:mm:ss')
                    : '--'}
                </Descriptions.Item>
              </Descriptions>
            </Col>

            <Col span={24}>
              <Descriptions column={1}>
                <Descriptions.Item label="告警正文">
                  {alarmDetailInfo.alarmContent || '--'}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={24}>
              <Descriptions column={1}>
                <Descriptions.Item label="告警值">
                  {alarmDetailInfo.alarmValue || '--'}
                </Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={24}>
              <Descriptions column={2}>
                {deviceInfoByModuleTypeCiId &&
                  deviceInfoByModuleTypeCiId.map(v => (
                    <Descriptions.Item key={v.fieldName} label={v.fieldName}>
                      <span style={{ whiteSpace: 'break-spaces' }}>{v.fieldValue || '--'}</span>
                    </Descriptions.Item>
                  ))}
              </Descriptions>
            </Col>
          </Row>

          <div style={{ textAlign: 'center' }}>
            <a
              onClick={() => {
                goPage(`/resourceCenter/deviceMonitorDetail/${alarmDetailInfo.ciId}`, {
                  pname: '',
                  modelName: record.deviceType,
                  ciId: alarmDetailInfo.ciId,
                  moduleType: record.moduleType,
                });
                this.setState({ visible: false });
              }}
            >
              查看监控详情
            </a>
          </div>
        </Modal>
        <Modal
          title="告警评注"
          visible={remarkVisible}
          destroyOnClose={true}
          onCancel={() => {
            this.setState({ remarkVisible: false });
          }}
          style={{ minHeight: 160 }}
          width={550}
          footer={''}
        >
          <AlarmRemark remarkInfo={remarkInfo} initData={this.initData} />
        </Modal>
      </div>
    );
  }
}
