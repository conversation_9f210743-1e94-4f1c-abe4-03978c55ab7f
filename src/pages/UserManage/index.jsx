import React, { useMemo, useEffect, useState } from 'react';
import ProTable from 'demasia-pro-table';
import { <PERSON>, Button, message, Modal, Spin } from 'antd';
import CreateModal from './components/CreateModal';
import BatchAuth from './components/BatchAuth';
import { saveUser, getUsers, deleteUsers, updateUser, saveCiPermission } from './service';
import styles from './index.less';
import ResourceDataModal from '@/pages/UserGroupManage/components/ResourceDataModal';
import { connect } from 'dryad';
import { cityProvince } from '@/services/basePath';
import { decrypt } from '@/utils/EncryptDecryp';
import { hyposensitization, desensitizeEmail } from '@/utils/utils';
import request from 'ponshine-request';

const requestTable = async params => {
  const { current=1, pageSize=10, ...rest } = params;
  const response = await getUsers({
    start: pageSize * (current - 1),
    length: pageSize,
    ...rest,
  });
  return Promise.resolve({
    data: response.data,
    total: response.iTotalRecords,
  });
};

const UserMange = ({ user }) => {
  const [selectedRowKeys, setSelectedKeys] = React.useState([]);
  const [opType, setOpType] = React.useState(null);
  const [editingUser, setEditingUser] = React.useState();
  const actionRef = React.useRef();
  const formRef = React.useRef();
  const [resourceUserGroupId, setResourceUserGroupId] = React.useState();
  const [userId, setUserId] = React.useState();
  const [roleNameList, setRoleNameList] = useState([]);
  const [batchAuthVisible, setBatchAuthVisible] = useState(false);
  const [batchLoading, setBatchLoading] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);

  const handleAddUser = async fieldsValues => {
    try {
      const response = await saveUser(fieldsValues);

      if (response.state === 'SUCCESS') {
        message.success('新增成功');
        formRef.current?.resetFields();
        formRef.current?.submit();
        actionRef.current?.reload();
        setOpType(null);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      message.error('请求失败');
    }
  };

  const handleUpdateUser = async fieldsValues => {
    try {
      const response = await updateUser(fieldsValues);

      if (response.state === 'SUCCESS') {
        message.success('编辑成功');
        actionRef.current?.reload();
        setOpType(null);
      } else {
        message.error(response.message);
      }
    } catch (e) {
      message.error('请求失败');
    }
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '删除用户',
      content: '确定删除选中的用户吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const ids = selectedRowKeys;

        try {
          await deleteUsers({
            ids,
          });
          message.success('删除成功！');
          actionRef.current?.reload();
          actionRef.current?.clearSelected();
        } catch (e) {
          // eslint-disable-next-line no-console
        }
      },
    });
  };

  const handleSync = async () => {
    setSyncLoading(true);
    const response = await request('/api/user/syncUserInfo', {
      method: 'GET',
    });
    setSyncLoading(false);
    if (response.state == 200) {
      message.success(response.message);
      actionRef.current?.reload();
    } else {
      message.error(response.message);
    }
  };
  const handleBatchAuth = async () => {
    setBatchAuthVisible(true);
  };

  const handleBatchAuthSubmit = async values => {
    setBatchLoading(true);
    const response = await request('/api/user/bachUpdateGroup', {
      method: 'POST',
      data: {
        ids: selectedRowKeys?.join(','),
        roleId: values.roleId,
      },
    });
    setBatchLoading(false);
    if (response.state == 'SUCCESS') {
      message.success(response.message);
      actionRef.current?.reload();
      setBatchAuthVisible(false);
      setSelectedKeys([]);
    } else {
      message.error(response.message);
    }
  };

  const roleId = user.currentUser.roleId;
  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
    },
    {
      title: '手机号码',
      dataIndex: 'phonenum',
      width: 120,
      hideInSearch: true,
      render: (_, record) => {
        return hyposensitization(decrypt(_));
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      hideInSearch: true,
      render: (_, record) => {
        return desensitizeEmail(decrypt(_));
      },
    },
    {
      title: '用户组',
      dataIndex: 'roleName',
      formI
      // valueEnum: roleNameList.reduce((acc, item) => {
      //   acc[item] = item;
      //   return acc;
      // }, {}),
      // hideInSearch: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '状态',
      dataIndex: 'accountNonLocked',
      valueEnum: {
        false: {
          text: '禁用',
          status: 'Default',
        },
        true: {
          text: '启用',
          status: 'Success',
        },
      },
      filters: undefined, // 取消筛选
    },
    {
      title: '部门',
      dataIndex: 'departmentName',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 160,
      render: (_, record) => {
        return [
          <Button
            key="edit"
            icon="edit"
            size="small"
            onClick={() => {
              setEditingUser({ ...record, password: '********' });
              setOpType('edit');
            }}
          >
            编辑
          </Button>,
          cityProvince == 'SX' ? (
            <Button
              title="设备资源权限分配"
              type="link"
              icon="form"
              disabled={!(roleId == 1 && record.id !== 1)}
              onClick={() => {
                setResourceUserGroupId(record.roleId);
                setUserId(record.id);
              }}
            />
          ) : (
            ''
          ),
        ];
      },
    },
  ];

  const handleSetResourceUserData = async keys => {
    try {
      // roleId: resourceUserGroupId,
      let response = await saveCiPermission({ userId, nodeIds: keys.toString() });
      if (response.code == '200') {
        message.success(response.msg);
        setResourceUserGroupId(undefined);
        setUserId(undefined);
      } else {
        message.error(response.msg);
      }
    } catch {
      message.error('请求失败');
    }
  };

  const getRoleNameList = async () => {
    const response = await request('/api/user/getRoleList', {
      method: 'GET',
    });
    if (response.state == 'SUCCESS') {
      setRoleNameList(response?.data || []);
    } else {
      message.error(response.msg);
    }
  };
  useEffect(() => {
    getRoleNameList();
  }, []);

  return (
    <Spin spinning={syncLoading} tip="用户同步中，请稍等...">
      <Card>
        <ProTable // 隐藏修改表格size功能
          // size="small"
          // options={{ density: false }}
          actionRef={actionRef}
          formRef={formRef}
          columns={columns}
          request={requestTable}
          rowKey="id"
          pagination={{
            defaultCurrent: 1,
            defaultPageSize: 10,
            showSizeChanger: true,
            pageSizeOptions: ['5', '10', '15', '20'],
            showTotal: (total, [start, end]) => `第${start}-${end}条/总共${total}条`,
          }}
          params={{
            sord: 'desc',
            sidx: 'id',
          }}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => {
              setSelectedKeys(keys);
            },
          }}
          toolBarRender={() => [
            <Button
              key="add"
              size="default"
              type="primary"
              icon="plus"
              onClick={() => {
                setOpType('add');
              }}
            >
              新建
            </Button>,
            <Button
              key="delete"
              size="default"
              type="danger"
              icon="delete"
              disabled={selectedRowKeys.length === 0}
              onClick={handleDelete}
            >
              批量删除
            </Button>,
            <Button
              key="sync"
              type="primary"
              icon="sync"
              onClick={handleSync}
              loading={syncLoading}
            >
              立即同步
            </Button>,
            <Button
              key="batchAuth"
              type="primary"
              icon="form"
              onClick={handleBatchAuth}
              disabled={selectedRowKeys.length === 0}
            >
              批量授权
            </Button>,
          ]}
          search={{
            collapsed: false,
            optionRender: ({ searchText, resetText }, { form, submit }) => {
              return (
                <>
                  <Button
                    type="primary"
                    onClick={() => {
                      setSelectedKeys([]);
                      submit();
                    }}
                  >
                    {searchText}
                  </Button>
                  <Button
                    style={{ marginLeft: 10 }}
                    onClick={() => {
                      setSelectedKeys([]);
                      form.resetFields();
                      submit();
                    }}
                  >
                    {resetText}
                  </Button>
                </>
              );
            },
          }}
        />
        <CreateModal
          visible={!!opType}
          opType={opType}
          user={editingUser}
          onAddUser={handleAddUser}
          onUpdateUser={handleUpdateUser}
          onClose={() => {
            setOpType(null);
          }}
        />
        <ResourceDataModal
          popTitle="设备权限分配"
          serviceType="2"
          visible={!!resourceUserGroupId}
          resourceUserGroupId={resourceUserGroupId}
          userId={userId}
          onSetResourceData={handleSetResourceUserData}
          onClose={() => setResourceUserGroupId(undefined)}
        />
        {batchAuthVisible && (
          <BatchAuth
            visible={batchAuthVisible}
            onCancel={() => setBatchAuthVisible(false)}
            onSubmit={handleBatchAuthSubmit}
            confirmLoading={batchLoading}
          />
        )}
      </Card>
    </Spin>
  );
};

export default connect(({ user }) => ({
  user,
}))(UserMange);
